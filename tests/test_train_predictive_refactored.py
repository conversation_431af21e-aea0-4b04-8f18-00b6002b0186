"""
Test script for the refactored train_predictive.py module.
"""

import sys
import os
import numpy as np
import pandas as pd
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.train_predictive import PredictiveModelTrainer

def create_test_data(n_samples=1000, n_features=10):
    """Create synthetic test data for predictive modeling."""
    np.random.seed(42)
    
    # Generate features
    X = np.random.randn(n_samples, n_features)
    
    # Generate binary target with some relationship to features
    linear_combination = X[:, 0] + 0.5 * X[:, 1] - 0.3 * X[:, 2]
    probabilities = 1 / (1 + np.exp(-linear_combination))
    y = np.random.binomial(1, probabilities)
    
    # Create DataFrame
    feature_names = [f'feature_{i}' for i in range(n_features)]
    data = pd.DataFrame(X, columns=feature_names)
    data['target'] = y
    
    # Add some categorical features
    data['category_A'] = np.random.choice(['cat1', 'cat2', 'cat3'], n_samples)
    data['category_B'] = np.random.choice(['type1', 'type2'], n_samples)
    
    return data

def test_basic_functionality():
    """Test basic functionality of the refactored PredictiveModelTrainer."""
    print("Testing Basic Functionality")
    print("=" * 50)
    
    # Create test data
    data = create_test_data(500, 5)  # Smaller dataset for faster testing
    print(f"Created test data: {data.shape}")
    
    # Initialize trainer
    trainer = PredictiveModelTrainer(
        data=data,
        target_column='target',
        test_size=0.3,
        model_name='test_model'
    )
    
    print("✓ Trainer initialized successfully")
    
    # Test data preprocessing
    X, y = trainer.preprocess_data()
    print(f"✓ Data preprocessing: {X.shape}, target: {y.shape}")
    
    # Test data splitting
    X_train, X_test, y_train, y_test = trainer.split_data()
    print(f"✓ Data splitting: train {X_train.shape}, test {X_test.shape}")
    
    return trainer

def test_model_training():
    """Test training different model types."""
    print("\nTesting Model Training")
    print("=" * 50)
    
    data = create_test_data(300, 5)
    trainer = PredictiveModelTrainer(data=data, target_column='target')
    
    # Test models to train (subset for faster testing)
    test_models = [
        'random_forest',
        'logistic_regression'
    ]
    
    results = []
    
    for model_type in test_models:
        print(f"\nTesting {model_type}...")
        
        try:
            # Use smaller parameter grid for faster testing
            if model_type == 'random_forest':
                custom_params = {
                    'n_estimators': [50, 100],
                    'max_depth': [5, 10],
                    'min_samples_split': [2, 5]
                }
            else:
                custom_params = None
            
            model = trainer.train(
                model_type=model_type,
                custom_params=custom_params,
                cv_folds=3  # Fewer folds for faster testing
            )
            
            summary = trainer.get_model_summary()
            results.append({
                'model_type': model_type,
                'success': True,
                'cv_score': summary.get('cv_score'),
                'test_accuracy': summary.get('test_metrics', {}).get('accuracy')
            })
            
            print(f"  ✓ {model_type} trained successfully")
            print(f"    CV Score: {summary.get('cv_score', 'N/A')}")
            print(f"    Test Accuracy: {summary.get('test_metrics', {}).get('accuracy', 'N/A')}")
            
        except Exception as e:
            print(f"  ✗ {model_type} failed: {e}")
            results.append({
                'model_type': model_type,
                'success': False,
                'error': str(e)
            })
    
    return results

def test_prediction_functionality():
    """Test prediction capabilities."""
    print("\nTesting Prediction Functionality")
    print("=" * 50)
    
    # Create training and test data
    train_data = create_test_data(200, 5)
    test_data = create_test_data(50, 5)
    
    trainer = PredictiveModelTrainer(data=train_data, target_column='target')
    
    # Train a simple model
    custom_params = {'n_estimators': [50], 'max_depth': [5]}
    trainer.train(model_type='random_forest', custom_params=custom_params, cv_folds=3)
    
    # Test predictions
    predictions = trainer.predict(test_data.drop('target', axis=1))
    print(f"✓ Predictions generated: {predictions.shape}")
    
    # Test probability predictions
    try:
        probabilities = trainer.predict_proba(test_data.drop('target', axis=1))
        print(f"✓ Probability predictions: {probabilities.shape}")
    except Exception as e:
        print(f"✗ Probability prediction failed: {e}")
    
    # Test prediction accuracy
    true_labels = test_data['target'].values
    accuracy = (predictions == true_labels).mean()
    print(f"✓ Prediction accuracy on test data: {accuracy:.3f}")
    
    return accuracy

def test_error_handling():
    """Test error handling and edge cases."""
    print("\nTesting Error Handling")
    print("=" * 50)
    
    # Test invalid inputs
    try:
        # Empty DataFrame
        empty_data = pd.DataFrame()
        trainer = PredictiveModelTrainer(data=empty_data, target_column='target')
        print("✗ Should have failed with empty data")
    except ValueError:
        print("✓ Correctly handled empty data")
    
    # Test invalid target column
    try:
        data = create_test_data(100, 3)
        trainer = PredictiveModelTrainer(data=data, target_column='nonexistent')
        print("✗ Should have failed with invalid target column")
    except ValueError:
        print("✓ Correctly handled invalid target column")
    
    # Test invalid test_size
    try:
        data = create_test_data(100, 3)
        trainer = PredictiveModelTrainer(data=data, target_column='target', test_size=1.5)
        print("✗ Should have failed with invalid test_size")
    except ValueError:
        print("✓ Correctly handled invalid test_size")
    
    # Test unsupported model type
    try:
        data = create_test_data(100, 3)
        trainer = PredictiveModelTrainer(data=data, target_column='target')
        trainer.train(model_type='unsupported_model')
        print("✗ Should have failed with unsupported model")
    except ValueError:
        print("✓ Correctly handled unsupported model type")

def main():
    """Run all tests for the refactored predictive model trainer."""
    print("Refactored PredictiveModelTrainer Test Suite")
    print("=" * 60)
    
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce log noise during testing
    
    try:
        # Test basic functionality
        trainer = test_basic_functionality()
        
        # Test model training
        training_results = test_model_training()
        
        # Test prediction functionality
        prediction_accuracy = test_prediction_functionality()
        
        # Test error handling
        test_error_handling()
        
        # Summary
        print(f"\n{'='*60}")
        print("TEST SUMMARY")
        print(f"{'='*60}")
        
        successful_models = [r for r in training_results if r['success']]
        failed_models = [r for r in training_results if not r['success']]
        
        print(f"✓ Basic functionality: PASSED")
        print(f"✓ Model training: {len(successful_models)}/{len(training_results)} models successful")
        print(f"✓ Prediction accuracy: {prediction_accuracy:.3f}")
        print(f"✓ Error handling: PASSED")
        
        if failed_models:
            print(f"\nFailed models:")
            for model in failed_models:
                print(f"  - {model['model_type']}: {model['error']}")
        
        print(f"\n✓ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test suite failed: {e}")
        raise

if __name__ == "__main__":
    main()
