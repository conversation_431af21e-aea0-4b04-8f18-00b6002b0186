import unittest
import pandas as pd
from src.data import load_data as ld

class test_data_loading(unittest.TestCase):
    def test_load_data(self):
        data = ld.load_source_data()
        self.assertIsInstance(data, pd.DataFrame)
        self.assertGreater(data.shape[0], 0)  # Check if there are rows in the DataFrame
        self.assertGreater(data.shape[1], 0)  # Check if there are columns in the DataFrame
        
if __name__ == '__main__':
    unittest.main()