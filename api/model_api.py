import os, sys, pandas as pd, numpy as np, logging as lg

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware

from src.common.utils import load_model, load_metadata, get_model_save_path
from src.globals.basic_settings import app_settings

# Configure logging
lg.basicConfig(level=lg.INFO)
logger = lg.getLogger(__name__)

# Global variables for loaded models
predictive_models = None
causal_models = None

predictive_metadata = None
causal_metadata = None

predictive_model_path = None
causal_model_path = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for startup and shutdown."""
    # Startup
    global predictive_model, \
        causal_model, \
        predictive_metadata, \
        causal_metadata, \
        predictive_model_path, \
        causal_model_path

    try:
        logger.info("Loading models...")

        # Load predictive model
        predictive_model_path = get_model_save_path("predictive_model", use_user_dir=False)

        if predictive_model_path and os.path.exists(predictive_model_path):
            predictive_model = load_model(predictive_model_path)
            predictive_metadata = load_metadata(predictive_model_path)
            logger.info(f"Predictive model loaded from {predictive_model_path}")
        else:
            logger.error("Predictive model path is not set or does not exist.")

        # Load causal model
        causal_model_path = get_model_save_path("causal_model", use_user_dir=False)

        if causal_model_path and os.path.exists(causal_model_path):
            causal_model = load_model(causal_model_path)
            causal_metadata = load_metadata(causal_model_path)
            logger.info(f"Causal model loaded from {causal_model_path}")
        else:
            logger.error("Causal model path is not set or does not exist.")

        logger.info("Models loaded completed.")
    except Exception as e:
        logger.error(f"Error during startup: {e}")

    yield  # Yield control back to the FastAPI app

    # Shutdown
    logger.info("Shutting down models...")
    # Clean up resources if needed
    predictive_model = None
    causal_model = None
    predictive_metadata = None
    causal_metadata = None


app = FastAPI(
    title="Medical Appointment Model API",
    description="API for querying predictive and causal models for medical appointments no-shows",
    version="1.0.0",
    lifespan=lifespan  # Use the lifespan parameter
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for simplicity; adjust as needed
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class PredictionRequest(BaseModel):
    """Request model for predictions."""
    age: int = Field(..., ge=0, le=120, description="Patient age")
    gender: str = Field(..., description="Patient gender (M/F)")
    scholarship: int = Field(..., ge=0, le=1, description="Has scholarship (0/1)")
    hypertension: int = Field(..., ge=0, le=1, description="Has hypertension (0/1)")
    diabetes: int = Field(..., ge=0, le=1, description="Has diabetes (0/1)")
    alcoholism: int = Field(..., ge=0, le=1, description="Has alcoholism (0/1)")
    handicap: int = Field(..., ge=0, le=1, description="Handicap level (0-4)")
    sms_received: int = Field(..., ge=0, le=1, description="SMS received (0/1)")
    days_between: int = Field(..., description="Days between registration and appointment")
    appointment_day: str = Field(..., description="Day of week for appointment")


class PredictionResponse(BaseModel):
    """Response model for predictions"""
    prediction: int
    probability: float
    confidence: str
    model_info: Dict[str, Any]


class TreatmentEffectRequest(BaseModel):
    """Request model for treatment effect estimation."""
    age: int = Field(..., ge=0, le=20)
    gender: str
    scholarship: int = Field(..., ge=0, le=1)
    hypertension: int = Field(..., ge=0, le=1)
    diabetes: int = Field(..., ge=0, le=1)
    alcoholism: int = Field(..., ge=0, le=1)
    handicap: int = Field(..., ge=0, le=4)
    days_between: int
    appointment_day_of_week: str


class TreatmentEffectResponse(BaseModel):
    """Response model for treatment effect estimation."""
    treatment_effect: float
    interpretation: str
    confidence: str
    model_info: Dict[str, Any]


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Medical Appointment Model API",
        "version": "1.0.0",
        "description": "API for querying predictive and causal models for medical appointments no-shows",
        "status": "Running",
        "endpoints": {
            "health": "/health",
            "model_info": "/model_info",
            "predict": "/predict",
            "treatment_effect": "/treatment_effect",
            "docs": "/docs"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models_loaded": {
            "predictive_model": predictive_model is not None,
            "causal_model": causal_model is not None
        }
    }


@app.get("/model_info")
async def get_model_info():
    """Get Information about loaded models"""
    return {
        "predictive_model": {
            "loaded": predictive_model is not None,
            "metadata": predictive_metadata or None
        },
        "causal_model": {
            "loaded": causal_model is not None,
            "metadata": causal_metadata or None
        }
    }


@app.post("/predict", response_model=PredictionResponse)
async def predict_no_show(request: PredictionRequest):
    """Predict no-show for medical appointments"""
    if predictive_model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Predictive model is not loaded or available."
        )

    try:
        # Convert request to dataframe
        input_data = pd.DataFrame([request.dict()])

        # Handle categorical encoding (match training preprocessing)
        categorical_cols = ['gender', 'appointment_day_of_week']
        for col in categorical_cols:
            if col in input_data.columns:
                input_data[col] = pd.get_dummies(input_data[col], drop_first=True)

        # Ensure all expected features are present
        expected_features = predictive_metadata.get("feature_names", []) if predictive_metadata else []

        # Add missing features with default values
        for features in expected_features:
            if features not in input_data.columns:
                input_data[features] = 0

        # Reorder columns to match training
        if expected_features:
            input_data = input_data.reindex(columns=expected_features, fill_value=0)

        # Make prediction
        prediction = predictive_model.predict(input_data)[0]
        probability = predictive_model.predict_proba(input_data)[0][1]

        # Determine confidence level
        confidence = "High" if probability > 0.75 else "Medium" if probability > 0.5 else "Low"

        return PredictionResponse(
            prediction=int(prediction),
            probability=float(probability),
            confidence=confidence,
            model_info={
                "model_type": predictive_metadata.get('algorithm', 'Unknown') if predictive_metadata else 'Unknown',
                "features_used": len(expected_features)
            }
        )
    except Exception as e:
        logger.error(f"Error during prediction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Prediction failed: {str(e)}",
        ) from e


@app.post("/treatment_effect", response_model=TreatmentEffectResponse)
async def estimate_treatment_effect(request: TreatmentEffectRequest):
    """Estimate treatment effect for SMS Reminders..."""
    if causal_model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Causal model is not loaded or available."
        )

    try:
        # Convert request to dataframe
        input_data = pd.DataFrame([request.dict()])

        # Handle categorical encoding (match training preprocessing)
        categorical_cols = ['gender', 'appointment_day_of_week']
        for col in categorical_cols:
            if col in input_data.columns:
                input_data[col] = pd.get_dummies(input_data[col], drop_first=True)

        # Ensure all expected features are present
        expected_features = causal_metadata.get("feature_names", []) if causal_metadata else []

        # Add missing features with default values
        for feature in expected_features:
            if feature not in input_data.columns:
                input_data[feature] = 0

        # Reorder columns to match training
        if expected_features:
            input_data = input_data.reindex(columns=expected_features, fill_value=0)

        # Estimate treatment effect
        treatment_effect = causal_model.predict(input_data)[0]

        # Interpret the effect
        if treatment_effect < -0.1:
            interpretation = "SMS reminders significantly reduce no-shows probability."
        elif treatment_effect > -0.05:
            interpretation = "SMS reminders moderately reduce no-shows probability."
        elif treatment_effect > 0.05:
            interpretation = "SMS reminders have minimal effect on no-shows probability."
        else:
            interpretation = "SMS reminders any increase no-shows probability."

        # Determine confidence
        confidence = "High" if abs(treatment_effect) > 0.1 else "Medium" if abs(treatment_effect) > 0.05 else "Low"

        return TreatmentEffectResponse(
            treatment_effect=float(treatment_effect),
            interpretation=interpretation,
            confidence=confidence,
            model_info={
                "model_type": causal_metadata.get('algorithm', 'Unknown') if causal_metadata else 'Unknown',
                "features_used": len(expected_features)
            }
        )
    except Exception as e:
        logger.error(f"Error during treatment effect estimation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Treatment effect estimation failed: {str(e)}",
        ) from e


@app.post("/batch_predict")
async def batch_predict(requests: List[PredictionRequest]):
    """Make batch predictions."""
    if predictive_model is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Predictive model is not loaded or available."
        )
    try:
        results = []
        for request in requests:
            # Reuse the single prediction logic
            prediction_response = await predict_no_show(request)
            results.append(prediction_response.dict())

        return {"predictions": results, "count": len(results), "timestamp": datetime.now().isoformat()}

    except Exception as e:
        logger.error(f"Batch prediction error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch prediction failed: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="127.0.0.1", port=8000)