{"cells": [{"cell_type": "markdown", "id": "a06cf889925d266f", "metadata": {}, "source": ["# 5. Predictive Modeling"]}, {"cell_type": "code", "id": "d885463da1cd2c48", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:55.168277Z", "start_time": "2025-07-29T17:19:55.155235Z"}}, "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from imblearn.over_sampling import SMOTE\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.model_selection import train_test_split"], "outputs": [], "execution_count": 23}, {"cell_type": "code", "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:55.573244Z", "start_time": "2025-07-29T17:19:55.323931Z"}}, "source": "final_dt = pd.read_csv(\"C:/Research/Msc/CMM709/Project/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show_features.csv\")", "outputs": [], "execution_count": 24}, {"cell_type": "code", "id": "203f19ecf5ae3099", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:55.764555Z", "start_time": "2025-07-29T17:19:55.722920Z"}}, "source": ["final_dt"], "outputs": [{"data": {"text/plain": ["       age  scholarship  hypertension  diabetes  alcoholism  handicap  \\\n", "0       76            0             1         0           0         0   \n", "1       23            0             0         0           0         0   \n", "2       39            0             0         0           0         0   \n", "3       19            0             0         0           0         0   \n", "4       30            0             0         0           0         0   \n", "...    ...          ...           ...       ...         ...       ...   \n", "71954   56            0             0         0           0         0   \n", "71955   51            0             0         0           0         0   \n", "71956   21            0             0         0           0         0   \n", "71957   38            0             0         0           0         0   \n", "71958   54            0             0         0           0         0   \n", "\n", "       sms_received  no_show  lead_time  scheduled_hours  ...  \\\n", "0             False    False          2               18  ...   \n", "1             False     True          2               16  ...   \n", "2             False     True          2               16  ...   \n", "3             False    False          2               17  ...   \n", "4             False    False          2               16  ...   \n", "...             ...      ...        ...              ...  ...   \n", "71954          True    False         35               13  ...   \n", "71955          True    False         35               16  ...   \n", "71956          True    False         41                7  ...   \n", "71957          True    False         41                7  ...   \n", "71958          True    False         41                7  ...   \n", "\n", "       neighbourhood_CARATOÍRA  neighbourhood_CENTRO  neighbourhood_ITARARÉ  \\\n", "0                        False                 False                  False   \n", "1                        False                 False                  False   \n", "2                        False                 False                  False   \n", "3                        False                 False                  False   \n", "4                        False                 False                  False   \n", "...                        ...                   ...                    ...   \n", "71954                    False                 False                  False   \n", "71955                    False                 False                  False   \n", "71956                    False                 False                  False   \n", "71957                    False                 False                  False   \n", "71958                    False                 False                  False   \n", "\n", "       neighbourhood_JARDIM CAMBURI  neighbourhood_JARDIM DA PENHA  \\\n", "0                             False                          False   \n", "1                             False                          False   \n", "2                             False                          False   \n", "3                             False                          False   \n", "4                             False                          False   \n", "...                             ...                            ...   \n", "71954                         False                          False   \n", "71955                         False                          False   \n", "71956                         False                          False   \n", "71957                         False                          False   \n", "71958                         False                          False   \n", "\n", "       neighbourhood_JESUS DE NAZARETH  neighbourhood_MARIA ORTIZ  \\\n", "0                                False                      False   \n", "1                                False                      False   \n", "2                                False                      False   \n", "3                                False                      False   \n", "4                                False                      False   \n", "...                                ...                        ...   \n", "71954                            False                       True   \n", "71955                            False                       True   \n", "71956                            False                       True   \n", "71957                            False                       True   \n", "71958                            False                       True   \n", "\n", "       neighbourhood_Other  neighbourhood_RESISTÊNCIA  \\\n", "0                     True                      False   \n", "1                     True                      False   \n", "2                     True                      False   \n", "3                     True                      False   \n", "4                     True                      False   \n", "...                    ...                        ...   \n", "71954                False                      False   \n", "71955                False                      False   \n", "71956                False                      False   \n", "71957                False                      False   \n", "71958                False                      False   \n", "\n", "       neighbourhood_TABUAZEIRO  \n", "0                         False  \n", "1                         False  \n", "2                         False  \n", "3                         False  \n", "4                         False  \n", "...                         ...  \n", "71954                     False  \n", "71955                     False  \n", "71956                     False  \n", "71957                     False  \n", "71958                     False  \n", "\n", "[71959 rows x 35 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>scheduled_hours</th>\n", "      <th>...</th>\n", "      <th>neighbourhood_CARATOÍRA</th>\n", "      <th>neighbourhood_CENTRO</th>\n", "      <th>neighbourhood_ITARARÉ</th>\n", "      <th>neighbourhood_JARDIM CAMBURI</th>\n", "      <th>neighbourhood_JARDIM DA PENHA</th>\n", "      <th>neighbourhood_JESUS DE NAZARETH</th>\n", "      <th>neighbourhood_MARIA ORTIZ</th>\n", "      <th>neighbourhood_Other</th>\n", "      <th>neighbourhood_RESISTÊNCIA</th>\n", "      <th>neighbourhood_TABUAZEIRO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>18</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>13</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>21</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>38</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 35 columns</p>\n", "</div>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "execution_count": 25}, {"cell_type": "code", "id": "92f89aa9cace03aa", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:56.042598Z", "start_time": "2025-07-29T17:19:56.015466Z"}}, "source": ["final_dt.info()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 35 columns):\n", " #   Column                             Non-Null Count  Dtype\n", "---  ------                             --------------  -----\n", " 0   age                                71959 non-null  int64\n", " 1   scholarship                        71959 non-null  int64\n", " 2   hypertension                       71959 non-null  int64\n", " 3   diabetes                           71959 non-null  int64\n", " 4   alcoholism                         71959 non-null  int64\n", " 5   handicap                           71959 non-null  int64\n", " 6   sms_received                       71959 non-null  bool \n", " 7   no_show                            71959 non-null  bool \n", " 8   lead_time                          71959 non-null  int64\n", " 9   scheduled_hours                    71959 non-null  int64\n", " 10  is_weekend                         71959 non-null  int64\n", " 11  days_until_next_appointment        71959 non-null  int64\n", " 12  previous_no_show                   71959 non-null  int64\n", " 13  total_appointments                 71959 non-null  int64\n", " 14  gender_M                           71959 non-null  bool \n", " 15  age_group_19-35                    71959 non-null  bool \n", " 16  age_group_36-60                    71959 non-null  bool \n", " 17  age_group_51-65                    71959 non-null  bool \n", " 18  age_group_66+                      71959 non-null  bool \n", " 19  received_sms_Yes                   71959 non-null  bool \n", " 20  appointment_day_of_week_Monday     71959 non-null  bool \n", " 21  appointment_day_of_week_Saturday   71959 non-null  bool \n", " 22  appointment_day_of_week_Thursday   71959 non-null  bool \n", " 23  appointment_day_of_week_Tuesday    71959 non-null  bool \n", " 24  appointment_day_of_week_Wednesday  71959 non-null  bool \n", " 25  neighbourhood_CARATOÍRA            71959 non-null  bool \n", " 26  neighbourhood_CENTRO               71959 non-null  bool \n", " 27  neighbourhood_ITARARÉ              71959 non-null  bool \n", " 28  neighbourhood_JARDIM CAMBURI       71959 non-null  bool \n", " 29  neighbourhood_JARDIM DA PENHA      71959 non-null  bool \n", " 30  neighbourhood_JESUS DE NAZARETH    71959 non-null  bool \n", " 31  neighbourhood_MARIA ORTIZ          71959 non-null  bool \n", " 32  neighbourhood_Other                71959 non-null  bool \n", " 33  neighbourhood_RESISTÊNCIA          71959 non-null  bool \n", " 34  neighbourhood_TABUAZEIRO           71959 non-null  bool \n", "dtypes: bool(23), int64(12)\n", "memory usage: 8.2 MB\n"]}], "execution_count": 26}, {"cell_type": "markdown", "id": "c1ff9d1dbe1c56dd", "metadata": {}, "source": ["## 5.1 Split the Dataset"]}, {"cell_type": "code", "id": "f2eaf90576f4fd55", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:56.534187Z", "start_time": "2025-07-29T17:19:56.301734Z"}}, "source": ["# Define features (x) and target (y)\n", "x_reduced = final_dt.drop([col for col in final_dt.columns if col.startswith('neighbourhood_') or col == 'no_show'], axis=1)\n", "y_reduced = final_dt['no_show']\n", "\n", "# Convert categorical variables to numerical using one-hot encoding\n", "X_reduced_encoded = pd.get_dummies(x_reduced, drop_first=True)\n", "\n", "# Impute missing values with the mean (or median/mode)\n", "impute = SimpleImputer(strategy='mean')\n", "X_reduced_imputed = pd.DataFrame(impute.fit_transform(X_reduced_encoded), columns=X_reduced_encoded.columns)\n", "\n", "# Split the dataset (80% train, 20% test)\n", "X_train_reduced, X_test_reduced, y_train_reduced, y_test_reduced = train_test_split(X_reduced_imputed, y_reduced, test_size=0.2, random_state=42, stratify=y_reduced)\n", "\n", "# Print the shapes of the resulting datasets\n", "print(f\"Training set shape: {X_train_reduced.shape}\")\n", "print(f\"Test set shape: {X_test_reduced.shape}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set shape: (57567, 24)\n", "Test set shape: (14392, 24)\n"]}], "execution_count": 27}, {"cell_type": "markdown", "id": "16aede62226b6c17", "metadata": {}, "source": ["## 5.2 Train and Evaluate Models\n", "<p>\n", "  Train and evaluate three models\n", "    <ol>\n", "       <li>Logistic Regression</li>\n", "       <li>Random Forest</li>\n", "       <li>XGBoost</li>\n", "    </ol>\n", "</p>"]}, {"cell_type": "code", "id": "3e5ff1c690322fe1", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:56.847670Z", "start_time": "2025-07-29T17:19:56.838809Z"}}, "source": ["from xgboost import XGBClassifier\n", "from collections import Counter\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, classification_report"], "outputs": [], "execution_count": 28}, {"cell_type": "code", "id": "e927863e596ab3e7", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:56.993275Z", "start_time": "2025-07-29T17:19:56.975139Z"}}, "source": ["# Function to handle class imbalance\n", "def handle_class_imbalance(X_train, y_train):\n", "    # Apply SMOTE to handle class imbalance\n", "    smote = SMOTE(random_state=RANDOM_STATE, sampling_strategy='auto', k_neighbors=5)\n", "    X_resampled, y_resampled = smote.fit_resample(X_train, y_train)\n", "\n", "    # Print the class distribution before and after SMOTE\n", "    print(\"Class Imbalance handling SMOTE\")\n", "    print(\"------------------------------\")\n", "    print(f\"Before SMOTE: {Counter(y_train)}\")\n", "    print(f\"After SMOTE: {Counter(y_resampled)}\")\n", "\n", "    return X_resampled, y_resampled\n", "\n", "# Function to evaluate model performance\n", "def evaluate_model(model, X_train_resampled, y_train_resampled, X_test, y_test):\n", "\n", "    # Train the model\n", "    model.fit(X_train_resampled, y_train_resampled)\n", "\n", "    # Make predictions\n", "    y_pred = model.predict(X_test)\n", "    y_prob = model.predict_proba(X_test)[:, 1]  # Get probabilities for the positive class\n", "\n", "    # Evaluate to model\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    precision = precision_score(y_test, y_pred)\n", "    recall = recall_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred)\n", "    roc_auc = roc_auc_score(y_test, y_prob)  # ROC AUC score\n", "\n", "    cm = confusion_matrix(y_test, y_pred)\n", "\n", "    graph_report = classification_report(y_test, y_pred, output_dict=True)\n", "    class_report = pd.DataFrame(graph_report).transpose()\n", "\n", "    # Print evaluation metrics\n", "    print(\"\\n\")\n", "    print(f\"Evaluation Metrics:\")\n", "    print(\"--------------------\")\n", "    print(f\"Accuracy: {accuracy:.4f}\")\n", "    print(f\"Precision: {precision:.4f}\")\n", "    print(f\"Recall: {recall:.4f}\")\n", "    print(f\"F1 Score: {f1:.4f}\")\n", "    print(f\"ROC AUC Score: {roc_auc:.4f}\")\n", "\n", "    # Plot confusion matrix\n", "    print(\"\\n\")\n", "    print(\"Evaluation:\")\n", "    print(\"-----------------\")\n", "    plt.figure(figsize=(8, 4))\n", "\n", "    plt.subplot(1, 2, 1)\n", "    sns.heatmap(class_report, annot=True, fmt='.2f', cmap='crest', cbar=False)\n", "    plt.title('Classification Report')\n", "    plt.xlabel('Metrics')\n", "    plt.ylabel('Classes')\n", "\n", "    plt.subplot(1, 2, 2)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Greens', cbar=False)\n", "    plt.title('Confusion Matrix')\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    return model\n", "\n", "RANDOM_STATE = 42  # Random state for reproducibility\n"], "outputs": [], "execution_count": 29}, {"cell_type": "code", "id": "9aa43b08870b7caa", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:19:57.153667Z", "start_time": "2025-07-29T17:19:57.144871Z"}}, "source": ["# Initialize models\n", "logistic_regression_model = LogisticRegression(random_state=RANDOM_STATE, max_iter=10000)\n", "random_forest_classifier_model = RandomForestClassifier(n_estimators=200, random_state=RANDOM_STATE)\n", "XGBClassifier_model = XGBClassifier(n_estimators=200, random_state=RANDOM_STATE)"], "outputs": [], "execution_count": 30}, {"cell_type": "markdown", "id": "9629cba221e97205", "metadata": {}, "source": ["### 5.2.1 Model Evaluation"]}, {"cell_type": "markdown", "id": "a585a9e890d68175", "metadata": {}, "source": ["#### i. Evaluate Logistic Regression"]}, {"cell_type": "code", "id": "cfaa33bacef78484", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:20:01.903856Z", "start_time": "2025-07-29T17:19:57.342550Z"}}, "source": ["X_lr_train_resampled, y_lr_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(logistic_regression_model, X_lr_train_resampled, y_lr_train_resampled, X_test_reduced, y_test_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.5630\n", "Precision: 0.3497\n", "Recall: 0.6192\n", "F1 Score: 0.4469\n", "ROC AUC Score: 0.6115\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"text/plain": ["<Figure size 800x400 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["LogisticRegression(max_iter=10000, random_state=42)"], "text/html": ["<style>#sk-container-id-10 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-10 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-10 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-10 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-10 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-10 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-10 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-10 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-10 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-10 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-10 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-10 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-10 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-10 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-10 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-10 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-10 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-10 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-10 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-10 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-10 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-10 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-10 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-10 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-10 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-10 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-10 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-10 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-10\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression(max_iter=10000, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-22\" type=\"checkbox\" checked><label for=\"sk-estimator-id-22\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(max_iter=10000, random_state=42)</pre></div> </div></div></div></div>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "execution_count": 31}, {"cell_type": "markdown", "id": "fbfd4ab6870c95cc", "metadata": {}, "source": ["#### ii. Eva<PERSON>ate Random Forest Classifier"]}, {"cell_type": "code", "id": "2ad2fdabdb3fc119", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:20:31.675322Z", "start_time": "2025-07-29T17:20:02.035130Z"}}, "source": ["X_rf_train_resampled, y_rf_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(random_forest_classifier_model, X_rf_train_resampled, y_rf_train_resampled, X_test_reduced, y_test_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.6806\n", "Precision: 0.3734\n", "Recall: 0.1771\n", "F1 Score: 0.2403\n", "ROC AUC Score: 0.5695\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"text/plain": ["<Figure size 800x400 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["RandomForestClassifier(n_estimators=200, random_state=42)"], "text/html": ["<style>#sk-container-id-11 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-11 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-11 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-11 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-11 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-11 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-11 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-11 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-11 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-11 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-11 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-11 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-11 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-11 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-11 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-11 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-11 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-11 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-11 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-11 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-11 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-11 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-11 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-11 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-11 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-11 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-11 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-11 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-11 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-11 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-11 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-11 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-11 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-11 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-11 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-11 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-11 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-11 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-11 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-11 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-11 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-11 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-11\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestClassifier(n_estimators=200, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-23\" type=\"checkbox\" checked><label for=\"sk-estimator-id-23\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomForestClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(n_estimators=200, random_state=42)</pre></div> </div></div></div></div>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "execution_count": 32}, {"cell_type": "markdown", "id": "457964c1302fce34", "metadata": {}, "source": ["#### iii. Evaluate XGBoost Classifier"]}, {"cell_type": "code", "id": "84a9e6e4d90bbafc", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:20:33.251449Z", "start_time": "2025-07-29T17:20:31.799215Z"}}, "source": ["X_xgb_train_resampled, y_xgb_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(XGBClassifier_model, X_xgb_train_resampled, y_xgb_train_resampled, X_test_reduced, y_test_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.7089\n", "Precision: 0.4594\n", "Recall: 0.1187\n", "F1 Score: 0.1886\n", "ROC AUC Score: 0.6072\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"text/plain": ["<Figure size 800x400 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)"], "text/html": ["<style>#sk-container-id-12 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-12 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-12 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-12 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-12 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-12 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-12 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-12 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-12 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-12 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-12 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-12 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-12 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-12 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-12 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-12 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-12 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-12 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-12 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-12 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-12 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-12 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-12 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-12 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-12 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-12 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-12 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-12 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-12 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-12 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-12 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-12 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-12 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-12 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-12 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-12 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-12 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-12 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-12 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-12 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-12 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-12 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-12\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-24\" type=\"checkbox\" checked><label for=\"sk-estimator-id-24\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;XGBClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "execution_count": 33}, {"cell_type": "markdown", "id": "8c4929f5d44ca215", "metadata": {}, "source": ["### 5.3 Hyperparameter Tuning\n", "<p>\n", "Using `GridSearchCV` to tune `Hyperparameter` for the best-performing model, Random Forest\n", "</p>"]}, {"cell_type": "code", "id": "74f67fb2391b6181", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:20:33.375908Z", "start_time": "2025-07-29T17:20:33.369040Z"}}, "source": ["from sklearn.model_selection import GridSearchCV, RandomizedSearchCV"], "outputs": [], "execution_count": 34}, {"cell_type": "code", "id": "d7d3d4148d2d2085", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:20:33.482146Z", "start_time": "2025-07-29T17:20:33.472271Z"}}, "source": ["# Define the parameter grid for each model\n", "\n", "param_grid_log_reg_a = {\n", "    'C': [0.1, 1.0, 10.0],         # Regularization strength\n", "    'penalty': ['l1','l2'],        # Type of regularization\n", "    'solver': ['liblinear','saga'] # Solver for optimization\n", "}\n", "\n", "param_grid_log_reg_ab = {\n", "    'C': [0.1],                            # Regularization strength\n", "    'penalty': ['l2'],                     # Type of regularization\n", "    'solver': ['lbfgs','newton-cholesky']  # Solver for optimization\n", "}\n", "\n", "param_grid_random_forest_a = {\n", "    'n_estimators': [50, 100, 200],  # Number of trees in the forest\n", "    'max_depth': [None, 10, 20, 30], # Maximum depth of the tree\n", "    'min_samples_split': [2, 5, 10], # Minimum number of samples required to split an internal node\n", "    'min_samples_leaf': [1, 2, 4]    # Minimum number of samples required to be at a leaf node\n", "}\n", "\n", "param_grid_xgb_a = {\n", "    'n_estimators': [50, 100, 200],       # Number of trees in the ensemble\n", "    'learning_rate': [0.01, 0.1, 0.2],    # Step size shrinkage used in update to prevent overfitting\n", "    'max_depth': [3, 5, 7],               # Maximum depth of a tree\n", "    'subsample': [0.8, 0.9, 1.0],         # Subsample ratio of the training instances\n", "    'colsample_bytree': [0.8, 0.9, 1.0]   # Subsample ratio of columns when constructing each tree\n", "}"], "outputs": [], "execution_count": 35}, {"cell_type": "markdown", "id": "70df756a174e31f7", "metadata": {}, "source": ["#### 1. GridSearchCV"]}, {"cell_type": "code", "id": "d2fbd3ad0a2bf2ed", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T18:11:29.544928Z", "start_time": "2025-07-29T18:11:25.133108Z"}}, "source": ["# Performance GridSearchCV for Logistic Regression\n", "print(\"Tuning Logistic Regression....\")\n", "grid_log_reg = GridSearchCV(estimator=logistic_regression_model, param_grid=param_grid_log_reg_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_log_reg.fit(X_train_reduced, y_train_reduced)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tuning Logistic Regression....\n", "Fitting 5 folds for each of 12 candidates, totalling 60 fits\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[49], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTuning Logistic Regression....\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      3\u001b[0m grid_log_reg \u001b[38;5;241m=\u001b[39m GridSearchCV(estimator\u001b[38;5;241m=\u001b[39mlogistic_regression_model, param_grid\u001b[38;5;241m=\u001b[39mparam_grid_log_reg_a, cv\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mf1\u001b[39m\u001b[38;5;124m'\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m3\u001b[39m)\n\u001b[1;32m----> 4\u001b[0m grid_log_reg\u001b[38;5;241m.\u001b[39mfit(X_train_reduced, y_train_reduced)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:1018\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m   1012\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_results(\n\u001b[0;32m   1013\u001b[0m         all_candidate_params, n_splits, all_out, all_more_results\n\u001b[0;32m   1014\u001b[0m     )\n\u001b[0;32m   1016\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[1;32m-> 1018\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_run_search(evaluate_candidates)\n\u001b[0;32m   1020\u001b[0m \u001b[38;5;66;03m# multimetric is determined here because in the case of a callable\u001b[39;00m\n\u001b[0;32m   1021\u001b[0m \u001b[38;5;66;03m# self.scoring the return type is only known after calling\u001b[39;00m\n\u001b[0;32m   1022\u001b[0m first_test_score \u001b[38;5;241m=\u001b[39m all_out[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest_scores\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:1572\u001b[0m, in \u001b[0;36mGridSearchCV._run_search\u001b[1;34m(self, evaluate_candidates)\u001b[0m\n\u001b[0;32m   1570\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_run_search\u001b[39m(\u001b[38;5;28mself\u001b[39m, evaluate_candidates):\n\u001b[0;32m   1571\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Search all candidates in param_grid\"\"\"\u001b[39;00m\n\u001b[1;32m-> 1572\u001b[0m     evaluate_candidates(ParameterGrid(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparam_grid))\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:964\u001b[0m, in \u001b[0;36mBaseSearchCV.fit.<locals>.evaluate_candidates\u001b[1;34m(candidate_params, cv, more_results)\u001b[0m\n\u001b[0;32m    956\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbose \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    957\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m    958\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFitting \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m folds for each of \u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m candidates,\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    959\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m totalling \u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m fits\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    960\u001b[0m             n_splits, n_candidates, n_candidates \u001b[38;5;241m*\u001b[39m n_splits\n\u001b[0;32m    961\u001b[0m         )\n\u001b[0;32m    962\u001b[0m     )\n\u001b[1;32m--> 964\u001b[0m out \u001b[38;5;241m=\u001b[39m parallel(\n\u001b[0;32m    965\u001b[0m     delayed(_fit_and_score)(\n\u001b[0;32m    966\u001b[0m         clone(base_estimator),\n\u001b[0;32m    967\u001b[0m         X,\n\u001b[0;32m    968\u001b[0m         y,\n\u001b[0;32m    969\u001b[0m         train\u001b[38;5;241m=\u001b[39mtrain,\n\u001b[0;32m    970\u001b[0m         test\u001b[38;5;241m=\u001b[39mtest,\n\u001b[0;32m    971\u001b[0m         parameters\u001b[38;5;241m=\u001b[39mparameters,\n\u001b[0;32m    972\u001b[0m         split_progress\u001b[38;5;241m=\u001b[39m(split_idx, n_splits),\n\u001b[0;32m    973\u001b[0m         candidate_progress\u001b[38;5;241m=\u001b[39m(cand_idx, n_candidates),\n\u001b[0;32m    974\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_and_score_kwargs,\n\u001b[0;32m    975\u001b[0m     )\n\u001b[0;32m    976\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m (cand_idx, parameters), (split_idx, (train, test)) \u001b[38;5;129;01min\u001b[39;00m product(\n\u001b[0;32m    977\u001b[0m         \u001b[38;5;28menumerate\u001b[39m(candidate_params),\n\u001b[0;32m    978\u001b[0m         \u001b[38;5;28menumerate\u001b[39m(cv\u001b[38;5;241m.\u001b[39msplit(X, y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39msplitter\u001b[38;5;241m.\u001b[39msplit)),\n\u001b[0;32m    979\u001b[0m     )\n\u001b[0;32m    980\u001b[0m )\n\u001b[0;32m    982\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(out) \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    983\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    984\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo fits were performed. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    985\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWas the CV iterator empty? \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    986\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWere there no candidates?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    987\u001b[0m     )\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\parallel.py:74\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     69\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     70\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     71\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     72\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     73\u001b[0m )\n\u001b[1;32m---> 74\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__call__\u001b[39m(iterable_with_config)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mlist\u001b[39m(output)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:1762\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1757\u001b[0m \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n\u001b[0;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m ((\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m\n\u001b[0;32m   1760\u001b[0m     (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mget_status(\n\u001b[0;32m   1761\u001b[0m         timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout) \u001b[38;5;241m==\u001b[39m TASK_PENDING)):\n\u001b[1;32m-> 1762\u001b[0m     time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m0.01\u001b[39m)\n\u001b[0;32m   1763\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[0;32m   1765\u001b[0m \u001b[38;5;66;03m# We need to be careful: the job list can be filling up as\u001b[39;00m\n\u001b[0;32m   1766\u001b[0m \u001b[38;5;66;03m# we empty it and Python list are not thread-safe by\u001b[39;00m\n\u001b[0;32m   1767\u001b[0m \u001b[38;5;66;03m# default hence the use of the lock\u001b[39;00m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "execution_count": 49}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:24:11.729708Z", "start_time": "2025-07-29T17:24:08.173048Z"}}, "cell_type": "code", "source": ["# Performance GridSearchCV for Logistic Regression using lbfgs and newton-cholesky\n", "print(\"\\nTuning Logistic Regression with lbfgs and newton-cholesky...\")\n", "grid_log_reg_ab = GridSearchCV(estimator=logistic_regression_model, param_grid=param_grid_log_reg_ab, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_log_reg_ab.fit(X_train_reduced, y_train_reduced)\n"], "id": "2e92edef13bf0ea9", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Logistic Regression with lbfgs and newton-cholesky...\n", "Fitting 5 folds for each of 2 candidates, totalling 10 fits\n"]}, {"data": {"text/plain": ["GridSearchCV(cv=5,\n", "             estimator=LogisticRegression(max_iter=10000, random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={'C': [0.1], 'penalty': ['l2'],\n", "                         'solver': ['lbfgs', 'newton-cholesky']},\n", "             scoring='f1', verbose=3)"], "text/html": ["<style>#sk-container-id-14 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-14 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-14 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-14 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-14 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-14 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-14 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-14 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-14 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-14 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-14 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-14 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-14 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-14 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-14 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-14 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-14 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-14 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-14 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-14 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-14 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-14 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-14 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-14 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-14 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-14 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-14 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-14 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-14 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-14 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-14 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-14 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-14 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-14 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-14 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-14 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-14 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-14 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-14 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-14 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-14 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-14 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-14\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>GridSearchCV(cv=5,\n", "             estimator=LogisticRegression(max_iter=10000, random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;C&#x27;: [0.1], &#x27;penalty&#x27;: [&#x27;l2&#x27;],\n", "                         &#x27;solver&#x27;: [&#x27;lbfgs&#x27;, &#x27;newton-cholesky&#x27;]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-28\" type=\"checkbox\" ><label for=\"sk-estimator-id-28\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;GridSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.GridSearchCV.html\">?<span>Documentation for GridSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>GridSearchCV(cv=5,\n", "             estimator=LogisticRegression(max_iter=10000, random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;C&#x27;: [0.1], &#x27;penalty&#x27;: [&#x27;l2&#x27;],\n", "                         &#x27;solver&#x27;: [&#x27;lbfgs&#x27;, &#x27;newton-cholesky&#x27;]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-29\" type=\"checkbox\" ><label for=\"sk-estimator-id-29\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: LogisticRegression</label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=0.1, max_iter=10000, random_state=42)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-30\" type=\"checkbox\" ><label for=\"sk-estimator-id-30\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=0.1, max_iter=10000, random_state=42)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "execution_count": 37}, {"cell_type": "code", "id": "1ed5561b312f1be9", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:31:13.145483Z", "start_time": "2025-07-29T17:24:11.814257Z"}}, "source": ["# Performance GridSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "grid_rf = GridSearchCV(estimator=random_forest_classifier_model, param_grid=param_grid_random_forest_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_rf.fit(X_train_reduced, y_train_reduced)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Random Forest...\n", "Fitting 5 folds for each of 108 candidates, totalling 540 fits\n"]}, {"data": {"text/plain": ["GridSearchCV(cv=5,\n", "             estimator=RandomForestClassifier(n_estimators=200,\n", "                                              random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={'max_depth': [None, 10, 20, 30],\n", "                         'min_samples_leaf': [1, 2, 4],\n", "                         'min_samples_split': [2, 5, 10],\n", "                         'n_estimators': [50, 100, 200]},\n", "             scoring='f1', verbose=3)"], "text/html": ["<style>#sk-container-id-15 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-15 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-15 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-15 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-15 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-15 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-15 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-15 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-15 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-15 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-15 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-15 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-15 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-15 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-15 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-15 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-15 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-15 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-15 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-15 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-15 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-15 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-15 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-15 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-15 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-15 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-15 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-15 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-15 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-15 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-15 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-15 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-15 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-15 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-15 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-15 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-15 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-15 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-15 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-15 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-15 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-15 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-15\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>GridSearchCV(cv=5,\n", "             estimator=RandomForestClassifier(n_estimators=200,\n", "                                              random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                         &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                         &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                         &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-31\" type=\"checkbox\" ><label for=\"sk-estimator-id-31\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;GridSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.GridSearchCV.html\">?<span>Documentation for GridSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>GridSearchCV(cv=5,\n", "             estimator=RandomForestClassifier(n_estimators=200,\n", "                                              random_state=42),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                         &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                         &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                         &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-32\" type=\"checkbox\" ><label for=\"sk-estimator-id-32\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: RandomForestClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(n_estimators=50, random_state=42)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-33\" type=\"checkbox\" ><label for=\"sk-estimator-id-33\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;RandomForestClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(n_estimators=50, random_state=42)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "execution_count": 38}, {"cell_type": "code", "id": "efd7b744107ec485", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:33:58.114414Z", "start_time": "2025-07-29T17:31:13.260837Z"}}, "source": ["# Performance GridSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "grid_xgb = GridSearchCV(estimator=XGBClassifier_model, param_grid=param_grid_xgb_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_xgb.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning XGBoost...\n", "Fitting 5 folds for each of 243 candidates, totalling 1215 fits\n"]}, {"data": {"text/plain": ["GridSearchCV(cv=5,\n", "             estimator=XGBClassifier(base_score=None, booster=None,\n", "                                     callbacks=None, colsample_bylevel=None,\n", "                                     colsample_bynode=None,\n", "                                     colsample_bytree=None, device=None,\n", "                                     early_stopping_rounds=None,\n", "                                     enable_categorical=False, eval_metric=None,\n", "                                     feature_types=None, feature_weights=None,\n", "                                     gamma=None, grow_policy=None,\n", "                                     importance_type=None,\n", "                                     interaction_constraints=Non...\n", "                                     max_delta_step=None, max_depth=None,\n", "                                     max_leaves=None, min_child_weight=None,\n", "                                     missing=nan, monotone_constraints=None,\n", "                                     multi_strategy=None, n_estimators=200,\n", "                                     n_jobs=None, num_parallel_tree=None, ...),\n", "             n_jobs=-1,\n", "             param_grid={'colsample_bytree': [0.8, 0.9, 1.0],\n", "                         'learning_rate': [0.01, 0.1, 0.2],\n", "                         'max_depth': [3, 5, 7], 'n_estimators': [50, 100, 200],\n", "                         'subsample': [0.8, 0.9, 1.0]},\n", "             scoring='f1', verbose=3)"], "text/html": ["<style>#sk-container-id-16 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-16 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-16 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-16 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-16 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-16 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-16 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-16 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-16 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-16 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-16 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-16 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-16 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-16 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-16 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-16 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-16 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-16 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-16 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-16 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-16 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-16 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-16 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-16 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-16 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-16 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-16 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-16 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-16 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-16 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-16 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-16 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-16 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-16 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-16 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-16 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-16 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-16 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-16 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-16 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-16 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-16 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-16\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>GridSearchCV(cv=5,\n", "             estimator=XGBClassifier(base_score=None, booster=None,\n", "                                     callbacks=None, colsample_bylevel=None,\n", "                                     colsample_bynode=None,\n", "                                     colsample_bytree=None, device=None,\n", "                                     early_stopping_rounds=None,\n", "                                     enable_categorical=False, eval_metric=None,\n", "                                     feature_types=None, feature_weights=None,\n", "                                     gamma=None, grow_policy=None,\n", "                                     importance_type=None,\n", "                                     interaction_constraints=Non...\n", "                                     max_delta_step=None, max_depth=None,\n", "                                     max_leaves=None, min_child_weight=None,\n", "                                     missing=nan, monotone_constraints=None,\n", "                                     multi_strategy=None, n_estimators=200,\n", "                                     n_jobs=None, num_parallel_tree=None, ...),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                         &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                         &#x27;max_depth&#x27;: [3, 5, 7], &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                         &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-34\" type=\"checkbox\" ><label for=\"sk-estimator-id-34\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;GridSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.GridSearchCV.html\">?<span>Documentation for GridSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>GridSearchCV(cv=5,\n", "             estimator=XGBClassifier(base_score=None, booster=None,\n", "                                     callbacks=None, colsample_bylevel=None,\n", "                                     colsample_bynode=None,\n", "                                     colsample_bytree=None, device=None,\n", "                                     early_stopping_rounds=None,\n", "                                     enable_categorical=False, eval_metric=None,\n", "                                     feature_types=None, feature_weights=None,\n", "                                     gamma=None, grow_policy=None,\n", "                                     importance_type=None,\n", "                                     interaction_constraints=Non...\n", "                                     max_delta_step=None, max_depth=None,\n", "                                     max_leaves=None, min_child_weight=None,\n", "                                     missing=nan, monotone_constraints=None,\n", "                                     multi_strategy=None, n_estimators=200,\n", "                                     n_jobs=None, num_parallel_tree=None, ...),\n", "             n_jobs=-1,\n", "             param_grid={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                         &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                         &#x27;max_depth&#x27;: [3, 5, 7], &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                         &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "             scoring=&#x27;f1&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-35\" type=\"checkbox\" ><label for=\"sk-estimator-id-35\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: XGBClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=1.0, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.2, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=7,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-36\" type=\"checkbox\" ><label for=\"sk-estimator-id-36\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=1.0, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.2, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=7,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "execution_count": 39}, {"cell_type": "code", "id": "69f6ba2b60d7934b", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:33:58.278694Z", "start_time": "2025-07-29T17:33:58.267073Z"}}, "source": ["print(f\"Logistic Regression Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression: {grid_log_reg.best_params_}\")\n", "print(f\"Best cross-validation score for Logistic Regression: {grid_log_reg.best_score_:.4f}\")\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest: {grid_rf.best_params_}\")\n", "print(f\"Best cross-validation score for Random Forest: {grid_rf.best_score_:.4f}\")\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost: {grid_xgb.best_params_}\")\n", "print(f\"Best cross-validation score for XGBoost: {grid_xgb.best_score_:.4f}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression Hyperparameter Tuning using GridSearchCV Results:\n", "---------------------------------------------------------------------\n", "Best parameters for Logistic Regression: {'C': 10.0, 'penalty': 'l1', 'solver': 'liblinear'}\n", "Best cross-validation score for Logistic Regression: 0.0501\n", "\n", "\n", "Random Forest Hyperparameter Tuning using GridSearchCV Results:\n", "---------------------------------------------------------------\n", "Best parameters for Random Forest: {'max_depth': None, 'min_samples_leaf': 1, 'min_samples_split': 2, 'n_estimators': 50}\n", "Best cross-validation score for Random Forest: 0.2276\n", "\n", "\n", "XGBoost Hyperparameter Tuning using GridSearchCV Results:\n", "---------------------------------------------------------\n", "Best parameters for XGBoost: {'colsample_bytree': 1.0, 'learning_rate': 0.2, 'max_depth': 7, 'n_estimators': 200, 'subsample': 0.8}\n", "Best cross-validation score for XGBoost: 0.2089\n"]}], "execution_count": 40}, {"cell_type": "code", "id": "460674e643091aca", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:36:31.855877Z", "start_time": "2025-07-29T17:33:58.427768Z"}}, "source": ["# Perform RandomizedSearchCV for Logistic Regression\n", "print(\"\\nTuning Logistic Regression...\")\n", "random_log_reg = RandomizedSearchCV(logistic_regression_model, param_grid_log_reg_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_log_reg.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Logistic Regression...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={'C': [0.1, 1.0, 10.0],\n", "                                        'penalty': ['l1', 'l2'],\n", "                                        'solver': ['liblinear', 'saga']},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-17 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-17 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-17 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-17 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-17 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-17 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-17 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-17 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-17 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-17 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-17 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-17 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-17 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-17 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-17 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-17 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-17 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-17 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-17 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-17 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-17 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-17 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-17 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-17 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-17 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-17 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-17 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-17 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-17 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-17 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-17 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-17 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-17 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-17 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-17 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-17 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-17 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-17 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-17 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-17 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-17 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-17 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-17\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;C&#x27;: [0.1, 1.0, 10.0],\n", "                                        &#x27;penalty&#x27;: [&#x27;l1&#x27;, &#x27;l2&#x27;],\n", "                                        &#x27;solver&#x27;: [&#x27;liblinear&#x27;, &#x27;saga&#x27;]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-37\" type=\"checkbox\" ><label for=\"sk-estimator-id-37\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;C&#x27;: [0.1, 1.0, 10.0],\n", "                                        &#x27;penalty&#x27;: [&#x27;l1&#x27;, &#x27;l2&#x27;],\n", "                                        &#x27;solver&#x27;: [&#x27;liblinear&#x27;, &#x27;saga&#x27;]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-38\" type=\"checkbox\" ><label for=\"sk-estimator-id-38\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: LogisticRegression</label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=10.0, max_iter=10000, penalty=&#x27;l1&#x27;, random_state=42,\n", "                   solver=&#x27;liblinear&#x27;)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-39\" type=\"checkbox\" ><label for=\"sk-estimator-id-39\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=10.0, max_iter=10000, penalty=&#x27;l1&#x27;, random_state=42,\n", "                   solver=&#x27;liblinear&#x27;)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "execution_count": 41}, {"cell_type": "code", "id": "fc318446e2b90b28", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:37:25.645908Z", "start_time": "2025-07-29T17:36:31.955446Z"}}, "source": ["# Perform RandomizedSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "random_rf = RandomizedSearchCV(random_forest_classifier_model, param_grid_random_forest_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_rf.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Random Forest...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={'max_depth': [None, 10, 20, 30],\n", "                                        'min_samples_leaf': [1, 2, 4],\n", "                                        'min_samples_split': [2, 5, 10],\n", "                                        'n_estimators': [50, 100, 200]},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-18 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-18 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-18 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-18 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-18 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-18 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-18 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-18 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-18 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-18 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-18 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-18 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-18 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-18 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-18 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-18 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-18 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-18 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-18 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-18 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-18 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-18 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-18 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-18 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-18 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-18 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-18 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-18 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-18 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-18 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-18 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-18 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-18 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-18 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-18 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-18 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-18 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-18 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-18 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-18 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-18 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-18 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-18\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                                        &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                                        &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-40\" type=\"checkbox\" ><label for=\"sk-estimator-id-40\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                                        &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                                        &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-41\" type=\"checkbox\" ><label for=\"sk-estimator-id-41\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: RandomForestClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(max_depth=10, n_estimators=200, random_state=42)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-42\" type=\"checkbox\" ><label for=\"sk-estimator-id-42\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;RandomForestClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(max_depth=10, n_estimators=200, random_state=42)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "execution_count": 42}, {"cell_type": "code", "id": "77caec97b4bf866a", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:37:31.175084Z", "start_time": "2025-07-29T17:37:25.659702Z"}}, "source": ["# Perform RandomizedSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "random_xgb = RandomizedSearchCV(XGBClassifier_model, param_grid_xgb_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_xgb.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning XGBoost...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   n_jobs=-1,\n", "                   param_distributions={'colsample_bytree': [0.8, 0.9, 1.0],\n", "                                        'learning_rate': [0.01, 0.1, 0.2],\n", "                                        'max_depth': [3, 5, 7],\n", "                                        'n_estimators': [50, 100, 200],\n", "                                        'subsample': [0.8, 0.9, 1.0]},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-19 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-19 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-19 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-19 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-19 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-19 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-19 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-19 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-19 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-19 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-19 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-19 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-19 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-19 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-19 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-19 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-19 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-19 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-19 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-19 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-19 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-19 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-19 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-19 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-19 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-19 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-19 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-19 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-19 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-19 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-19 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-19 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-19 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-19 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-19 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-19 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-19 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-19 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-19 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-19 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-19 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-19 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-19\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                                        &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                                        &#x27;max_depth&#x27;: [3, 5, 7],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                                        &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-43\" type=\"checkbox\" ><label for=\"sk-estimator-id-43\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   n_jobs=-1,\n", "                   param_distributions={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                                        &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                                        &#x27;max_depth&#x27;: [3, 5, 7],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                                        &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-44\" type=\"checkbox\" ><label for=\"sk-estimator-id-44\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: XGBClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=0.9, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.1, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=5,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=100,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-45\" type=\"checkbox\" ><label for=\"sk-estimator-id-45\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=0.9, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.1, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=5,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=100,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "execution_count": 43}, {"cell_type": "code", "id": "6e8deed15505322", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:37:31.363986Z", "start_time": "2025-07-29T17:37:31.357483Z"}}, "source": ["print(f\"Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression:\", random_log_reg.best_params_)\n", "print(f\"Best cross-validation score for Logistic Regression:\", random_log_reg.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest:\", random_rf.best_params_)\n", "print(f\"Best cross-validation score for Random Forest:\", random_rf.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost:\", random_xgb.best_params_)\n", "print(f\"Best cross-validation score for XGBoost:\", random_xgb.best_score_)\n", "print(f\"\\n\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------------------\n", "Best parameters for Logistic Regression: {'solver': 'liblinear', 'penalty': 'l1', 'C': 10.0}\n", "Best cross-validation score for Logistic Regression: 0.7162089132884818\n", "\n", "\n", "Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------------\n", "Best parameters for Random Forest: {'n_estimators': 200, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_depth': 10}\n", "Best cross-validation score for Random Forest: 0.7192835745971796\n", "\n", "\n", "XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------\n", "Best parameters for XGBoost: {'subsample': 0.9, 'n_estimators': 100, 'max_depth': 5, 'learning_rate': 0.1, 'colsample_bytree': 0.9}\n", "Best cross-validation score for XGBoost: 0.7207427644344799\n", "\n", "\n"]}], "execution_count": 44}, {"cell_type": "markdown", "id": "fd4867f0d039cf44", "metadata": {}, "source": ["#### 2. RandomizedSearchCV"]}, {"cell_type": "code", "id": "76263e49ab6a9b59", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:46:24.870331Z", "start_time": "2025-07-29T17:37:31.462056Z"}}, "source": ["# Perform RandomizedSearchCV for Logistic Regression\n", "print(\"\\nTuning Logistic Regression...\")\n", "random_log_reg = RandomizedSearchCV(logistic_regression_model, param_grid_log_reg_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_log_reg.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Logistic Regression...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n", "[CV 1/5] END C=10.0, penalty=l2, solver=liblinear;, score=0.716 total time=   0.1s\n", "[CV 2/5] END C=10.0, penalty=l2, solver=liblinear;, score=0.717 total time=   0.1s\n", "[CV 3/5] END C=10.0, penalty=l2, solver=liblinear;, score=0.715 total time=   0.1s\n", "[CV 4/5] END C=10.0, penalty=l2, solver=liblinear;, score=0.718 total time=   0.1s\n", "[CV 5/5] END C=10.0, penalty=l2, solver=liblinear;, score=0.714 total time=   0.1s\n", "[CV 1/5] END C=1.0, penalty=l1, solver=liblinear;, score=0.716 total time=   0.3s\n", "[CV 2/5] END C=1.0, penalty=l1, solver=liblinear;, score=0.717 total time=   0.2s\n", "[CV 3/5] END C=1.0, penalty=l1, solver=liblinear;, score=0.715 total time=   0.6s\n", "[CV 4/5] END C=1.0, penalty=l1, solver=liblinear;, score=0.718 total time=   0.6s\n", "[CV 5/5] END C=1.0, penalty=l1, solver=liblinear;, score=0.714 total time=   0.2s\n", "[CV 1/5] END ....C=1.0, penalty=l2, solver=saga;, score=0.716 total time=  22.5s\n", "[CV 2/5] END ....C=1.0, penalty=l2, solver=saga;, score=0.717 total time=  20.1s\n", "[CV 3/5] END ....C=1.0, penalty=l2, solver=saga;, score=0.715 total time=  23.6s\n", "[CV 4/5] END ....C=1.0, penalty=l2, solver=saga;, score=0.718 total time=  22.2s\n", "[CV 5/5] END ....C=1.0, penalty=l2, solver=saga;, score=0.714 total time=  18.9s\n", "[CV 1/5] END ....C=0.1, penalty=l1, solver=saga;, score=0.716 total time=  22.5s\n", "[CV 2/5] END ....C=0.1, penalty=l1, solver=saga;, score=0.717 total time=  20.5s\n", "[CV 3/5] END ....C=0.1, penalty=l1, solver=saga;, score=0.715 total time=  14.6s\n", "[CV 4/5] END ....C=0.1, penalty=l1, solver=saga;, score=0.718 total time=  10.8s\n", "[CV 5/5] END ....C=0.1, penalty=l1, solver=saga;, score=0.714 total time=  17.0s\n", "[CV 1/5] END ...C=10.0, penalty=l2, solver=saga;, score=0.716 total time=  23.1s\n", "[CV 2/5] END ...C=10.0, penalty=l2, solver=saga;, score=0.717 total time=  20.9s\n", "[CV 3/5] END ...C=10.0, penalty=l2, solver=saga;, score=0.715 total time=  22.3s\n", "[CV 4/5] END ...C=10.0, penalty=l2, solver=saga;, score=0.718 total time=  23.2s\n", "[CV 5/5] END ...C=10.0, penalty=l2, solver=saga;, score=0.714 total time=  22.7s\n", "[CV 1/5] END C=0.1, penalty=l2, solver=liblinear;, score=0.716 total time=   0.0s\n", "[CV 2/5] END C=0.1, penalty=l2, solver=liblinear;, score=0.717 total time=   0.0s\n", "[CV 3/5] END C=0.1, penalty=l2, solver=liblinear;, score=0.715 total time=   0.0s\n", "[CV 4/5] END C=0.1, penalty=l2, solver=liblinear;, score=0.718 total time=   0.0s\n", "[CV 5/5] END C=0.1, penalty=l2, solver=liblinear;, score=0.714 total time=   0.0s\n", "[CV 1/5] END C=10.0, penalty=l1, solver=liblinear;, score=0.716 total time=   0.1s\n", "[CV 2/5] END C=10.0, penalty=l1, solver=liblinear;, score=0.717 total time=   0.0s\n", "[CV 3/5] END C=10.0, penalty=l1, solver=liblinear;, score=0.715 total time=   0.1s\n", "[CV 4/5] END C=10.0, penalty=l1, solver=liblinear;, score=0.718 total time=   0.1s\n", "[CV 5/5] END C=10.0, penalty=l1, solver=liblinear;, score=0.714 total time=   0.1s\n", "[CV 1/5] END ....C=0.1, penalty=l2, solver=saga;, score=0.716 total time=  16.4s\n", "[CV 2/5] END ....C=0.1, penalty=l2, solver=saga;, score=0.718 total time=  17.2s\n", "[CV 3/5] END ....C=0.1, penalty=l2, solver=saga;, score=0.715 total time=  17.0s\n", "[CV 4/5] END ....C=0.1, penalty=l2, solver=saga;, score=0.718 total time=  16.8s\n", "[CV 5/5] END ....C=0.1, penalty=l2, solver=saga;, score=0.714 total time=  15.2s\n", "[CV 1/5] END C=1.0, penalty=l2, solver=liblinear;, score=0.716 total time=   0.0s\n", "[CV 2/5] END C=1.0, penalty=l2, solver=liblinear;, score=0.718 total time=   0.0s\n", "[CV 3/5] END C=1.0, penalty=l2, solver=liblinear;, score=0.715 total time=   0.1s\n", "[CV 4/5] END C=1.0, penalty=l2, solver=liblinear;, score=0.718 total time=   0.1s\n", "[CV 5/5] END C=1.0, penalty=l2, solver=liblinear;, score=0.714 total time=   0.0s\n", "[CV 1/5] END ...C=10.0, penalty=l1, solver=saga;, score=0.716 total time=  27.8s\n", "[CV 2/5] END ...C=10.0, penalty=l1, solver=saga;, score=0.717 total time=  25.6s\n", "[CV 3/5] END ...C=10.0, penalty=l1, solver=saga;, score=0.715 total time=  34.6s\n", "[CV 4/5] END ...C=10.0, penalty=l1, solver=saga;, score=0.718 total time=  24.8s\n", "[CV 5/5] END ...C=10.0, penalty=l1, solver=saga;, score=0.714 total time=  24.4s\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   param_distributions={'C': [0.1, 1.0, 10.0],\n", "                                        'penalty': ['l1', 'l2'],\n", "                                        'solver': ['liblinear', 'saga']},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-20 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-20 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-20 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-20 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-20 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-20 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-20 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-20 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-20 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-20 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-20 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-20 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-20 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-20 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-20 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-20 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-20 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-20 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-20 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-20 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-20 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-20 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-20 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-20 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-20 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-20 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-20 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-20 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-20 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-20 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-20 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-20 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-20 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-20 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-20 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-20 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-20 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-20 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-20 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-20 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-20 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-20 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-20\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   param_distributions={&#x27;C&#x27;: [0.1, 1.0, 10.0],\n", "                                        &#x27;penalty&#x27;: [&#x27;l1&#x27;, &#x27;l2&#x27;],\n", "                                        &#x27;solver&#x27;: [&#x27;liblinear&#x27;, &#x27;saga&#x27;]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-46\" type=\"checkbox\" ><label for=\"sk-estimator-id-46\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=LogisticRegression(max_iter=10000,\n", "                                                random_state=42),\n", "                   param_distributions={&#x27;C&#x27;: [0.1, 1.0, 10.0],\n", "                                        &#x27;penalty&#x27;: [&#x27;l1&#x27;, &#x27;l2&#x27;],\n", "                                        &#x27;solver&#x27;: [&#x27;liblinear&#x27;, &#x27;saga&#x27;]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-47\" type=\"checkbox\" ><label for=\"sk-estimator-id-47\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: LogisticRegression</label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=10.0, max_iter=10000, penalty=&#x27;l1&#x27;, random_state=42,\n", "                   solver=&#x27;liblinear&#x27;)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-48\" type=\"checkbox\" ><label for=\"sk-estimator-id-48\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(C=10.0, max_iter=10000, penalty=&#x27;l1&#x27;, random_state=42,\n", "                   solver=&#x27;liblinear&#x27;)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "execution_count": 45}, {"cell_type": "code", "id": "b9481caa65549601", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:48:52.937977Z", "start_time": "2025-07-29T17:46:24.981348Z"}}, "source": ["# Perform RandomizedSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "random_rf = RandomizedSearchCV(random_forest_classifier_model, param_grid_random_forest_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_rf.fit(X_train_reduced, y_train_reduced)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning Random Forest...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n", "[CV 1/5] END max_depth=None, min_samples_leaf=2, min_samples_split=5, n_estimators=200;, score=0.716 total time=   6.4s\n", "[CV 2/5] END max_depth=None, min_samples_leaf=2, min_samples_split=5, n_estimators=200;, score=0.716 total time=   7.2s\n", "[CV 3/5] END max_depth=None, min_samples_leaf=2, min_samples_split=5, n_estimators=200;, score=0.713 total time=   5.8s\n", "[CV 4/5] END max_depth=None, min_samples_leaf=2, min_samples_split=5, n_estimators=200;, score=0.717 total time=   5.8s\n", "[CV 5/5] END max_depth=None, min_samples_leaf=2, min_samples_split=5, n_estimators=200;, score=0.715 total time=   6.7s\n", "[CV 1/5] END max_depth=None, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.716 total time=   2.9s\n", "[CV 2/5] END max_depth=None, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.715 total time=   2.9s\n", "[CV 3/5] END max_depth=None, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.712 total time=   2.9s\n", "[CV 4/5] END max_depth=None, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.715 total time=   3.8s\n", "[CV 5/5] END max_depth=None, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.715 total time=   2.9s\n", "[CV 1/5] END max_depth=20, min_samples_leaf=2, min_samples_split=5, n_estimators=50;, score=0.717 total time=   1.2s\n", "[CV 2/5] END max_depth=20, min_samples_leaf=2, min_samples_split=5, n_estimators=50;, score=0.718 total time=   1.3s\n", "[CV 3/5] END max_depth=20, min_samples_leaf=2, min_samples_split=5, n_estimators=50;, score=0.717 total time=   1.3s\n", "[CV 4/5] END max_depth=20, min_samples_leaf=2, min_samples_split=5, n_estimators=50;, score=0.718 total time=   1.7s\n", "[CV 5/5] END max_depth=20, min_samples_leaf=2, min_samples_split=5, n_estimators=50;, score=0.717 total time=   1.5s\n", "[CV 1/5] END max_depth=10, min_samples_leaf=4, min_samples_split=5, n_estimators=100;, score=0.720 total time=   1.5s\n", "[CV 2/5] END max_depth=10, min_samples_leaf=4, min_samples_split=5, n_estimators=100;, score=0.720 total time=   1.9s\n", "[CV 3/5] END max_depth=10, min_samples_leaf=4, min_samples_split=5, n_estimators=100;, score=0.718 total time=   2.3s\n", "[CV 4/5] END max_depth=10, min_samples_leaf=4, min_samples_split=5, n_estimators=100;, score=0.719 total time=   1.6s\n", "[CV 5/5] END max_depth=10, min_samples_leaf=4, min_samples_split=5, n_estimators=100;, score=0.718 total time=   1.9s\n", "[CV 1/5] END max_depth=None, min_samples_leaf=1, min_samples_split=10, n_estimators=100;, score=0.715 total time=   3.2s\n", "[CV 2/5] END max_depth=None, min_samples_leaf=1, min_samples_split=10, n_estimators=100;, score=0.715 total time=   3.0s\n", "[CV 3/5] END max_depth=None, min_samples_leaf=1, min_samples_split=10, n_estimators=100;, score=0.711 total time=   3.8s\n", "[CV 4/5] END max_depth=None, min_samples_leaf=1, min_samples_split=10, n_estimators=100;, score=0.715 total time=   3.4s\n", "[CV 5/5] END max_depth=None, min_samples_leaf=1, min_samples_split=10, n_estimators=100;, score=0.713 total time=   2.9s\n", "[CV 1/5] END max_depth=20, min_samples_leaf=2, min_samples_split=2, n_estimators=200;, score=0.719 total time=   5.3s\n", "[CV 2/5] END max_depth=20, min_samples_leaf=2, min_samples_split=2, n_estimators=200;, score=0.721 total time=   6.2s\n", "[CV 3/5] END max_depth=20, min_samples_leaf=2, min_samples_split=2, n_estimators=200;, score=0.717 total time=   5.3s\n", "[CV 4/5] END max_depth=20, min_samples_leaf=2, min_samples_split=2, n_estimators=200;, score=0.718 total time=   5.3s\n", "[CV 5/5] END max_depth=20, min_samples_leaf=2, min_samples_split=2, n_estimators=200;, score=0.717 total time=   6.1s\n", "[CV 1/5] END max_depth=20, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   1.1s\n", "[CV 2/5] END max_depth=20, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   1.2s\n", "[CV 3/5] END max_depth=20, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.717 total time=   1.2s\n", "[CV 4/5] END max_depth=20, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   1.1s\n", "[CV 5/5] END max_depth=20, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   1.1s\n", "[CV 1/5] END max_depth=10, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.721 total time=   0.7s\n", "[CV 2/5] END max_depth=10, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   0.7s\n", "[CV 3/5] END max_depth=10, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.718 total time=   0.7s\n", "[CV 4/5] END max_depth=10, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.719 total time=   0.7s\n", "[CV 5/5] END max_depth=10, min_samples_leaf=4, min_samples_split=2, n_estimators=50;, score=0.718 total time=   0.7s\n", "[CV 1/5] END max_depth=None, min_samples_leaf=4, min_samples_split=2, n_estimators=100;, score=0.720 total time=   3.2s\n", "[CV 2/5] END max_depth=None, min_samples_leaf=4, min_samples_split=2, n_estimators=100;, score=0.720 total time=   2.7s\n", "[CV 3/5] END max_depth=None, min_samples_leaf=4, min_samples_split=2, n_estimators=100;, score=0.717 total time=   2.6s\n", "[CV 4/5] END max_depth=None, min_samples_leaf=4, min_samples_split=2, n_estimators=100;, score=0.719 total time=   3.0s\n", "[CV 5/5] END max_depth=None, min_samples_leaf=4, min_samples_split=2, n_estimators=100;, score=0.718 total time=   2.7s\n", "[CV 1/5] END max_depth=10, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.721 total time=   1.6s\n", "[CV 2/5] END max_depth=10, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.720 total time=   2.5s\n", "[CV 3/5] END max_depth=10, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.718 total time=   2.2s\n", "[CV 4/5] END max_depth=10, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.719 total time=   1.6s\n", "[CV 5/5] END max_depth=10, min_samples_leaf=2, min_samples_split=2, n_estimators=100;, score=0.717 total time=   1.5s\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   param_distributions={'max_depth': [None, 10, 20, 30],\n", "                                        'min_samples_leaf': [1, 2, 4],\n", "                                        'min_samples_split': [2, 5, 10],\n", "                                        'n_estimators': [50, 100, 200]},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-21 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-21 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-21 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-21 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-21 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-21 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-21 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-21 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-21 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-21 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-21 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-21 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-21 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-21 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-21 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-21 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-21 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-21 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-21 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-21 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-21 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-21 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-21 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-21 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-21 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-21 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-21 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-21 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-21 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-21 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-21 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-21 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-21 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-21 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-21 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-21 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-21 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-21 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-21 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-21 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-21 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-21 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-21\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   param_distributions={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                                        &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                                        &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-49\" type=\"checkbox\" ><label for=\"sk-estimator-id-49\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=RandomForestClassifier(n_estimators=200,\n", "                                                    random_state=42),\n", "                   param_distributions={&#x27;max_depth&#x27;: [None, 10, 20, 30],\n", "                                        &#x27;min_samples_leaf&#x27;: [1, 2, 4],\n", "                                        &#x27;min_samples_split&#x27;: [2, 5, 10],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-50\" type=\"checkbox\" ><label for=\"sk-estimator-id-50\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: RandomForestClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(max_depth=10, min_samples_leaf=2, random_state=42)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-51\" type=\"checkbox\" ><label for=\"sk-estimator-id-51\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;RandomForestClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(max_depth=10, min_samples_leaf=2, random_state=42)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "execution_count": 46}, {"cell_type": "code", "id": "eced647dc6443d58", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:49:00.919578Z", "start_time": "2025-07-29T17:48:53.035329Z"}}, "source": ["# Perform RandomizedSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "random_xgh = RandomizedSearchCV(XGBClassifier_model, param_grid_xgb_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_xgh.fit(X_train_reduced, y_train_reduced)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tuning XGBoost...\n", "Fitting 5 folds for each of 10 candidates, totalling 50 fits\n", "[CV 1/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=1.0;, score=0.718 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=1.0;, score=0.717 total time=   0.1s\n", "[CV 3/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=1.0;, score=0.717 total time=   0.1s\n", "[CV 4/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=1.0;, score=0.718 total time=   0.1s\n", "[CV 5/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=1.0;, score=0.717 total time=   0.1s\n", "[CV 1/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=0.9;, score=0.719 total time=   0.0s\n", "[CV 2/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=0.9;, score=0.722 total time=   0.0s\n", "[CV 3/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=0.9;, score=0.715 total time=   0.0s\n", "[CV 4/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=0.9;, score=0.721 total time=   0.0s\n", "[CV 5/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=0.9;, score=0.715 total time=   0.0s\n", "[CV 1/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=100, subsample=0.8;, score=0.722 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=100, subsample=0.8;, score=0.720 total time=   0.0s\n", "[CV 3/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=100, subsample=0.8;, score=0.716 total time=   0.0s\n", "[CV 4/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=100, subsample=0.8;, score=0.722 total time=   0.0s\n", "[CV 5/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.0s\n", "[CV 1/5] END colsample_bytree=1.0, learning_rate=0.2, max_depth=3, n_estimators=200, subsample=0.8;, score=0.721 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=1.0, learning_rate=0.2, max_depth=3, n_estimators=200, subsample=0.8;, score=0.721 total time=   0.1s\n", "[CV 3/5] END colsample_bytree=1.0, learning_rate=0.2, max_depth=3, n_estimators=200, subsample=0.8;, score=0.718 total time=   0.1s\n", "[CV 4/5] END colsample_bytree=1.0, learning_rate=0.2, max_depth=3, n_estimators=200, subsample=0.8;, score=0.720 total time=   0.1s\n", "[CV 5/5] END colsample_bytree=1.0, learning_rate=0.2, max_depth=3, n_estimators=200, subsample=0.8;, score=0.718 total time=   0.1s\n", "[CV 1/5] END colsample_bytree=1.0, learning_rate=0.01, max_depth=5, n_estimators=100, subsample=0.9;, score=0.717 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=1.0, learning_rate=0.01, max_depth=5, n_estimators=100, subsample=0.9;, score=0.717 total time=   0.1s\n", "[CV 3/5] END colsample_bytree=1.0, learning_rate=0.01, max_depth=5, n_estimators=100, subsample=0.9;, score=0.717 total time=   0.1s\n", "[CV 4/5] END colsample_bytree=1.0, learning_rate=0.01, max_depth=5, n_estimators=100, subsample=0.9;, score=0.717 total time=   0.1s\n", "[CV 5/5] END colsample_bytree=1.0, learning_rate=0.01, max_depth=5, n_estimators=100, subsample=0.9;, score=0.717 total time=   0.1s\n", "[CV 1/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=50, subsample=1.0;, score=0.721 total time=   0.0s\n", "[CV 2/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=50, subsample=1.0;, score=0.721 total time=   0.0s\n", "[CV 3/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=50, subsample=1.0;, score=0.718 total time=   0.0s\n", "[CV 4/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=50, subsample=1.0;, score=0.722 total time=   0.0s\n", "[CV 5/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=5, n_estimators=50, subsample=1.0;, score=0.719 total time=   0.0s\n", "[CV 1/5] END colsample_bytree=0.9, learning_rate=0.2, max_depth=3, n_estimators=100, subsample=1.0;, score=0.721 total time=   0.0s\n", "[CV 2/5] END colsample_bytree=0.9, learning_rate=0.2, max_depth=3, n_estimators=100, subsample=1.0;, score=0.720 total time=   0.0s\n", "[CV 3/5] END colsample_bytree=0.9, learning_rate=0.2, max_depth=3, n_estimators=100, subsample=1.0;, score=0.720 total time=   0.0s\n", "[CV 4/5] END colsample_bytree=0.9, learning_rate=0.2, max_depth=3, n_estimators=100, subsample=1.0;, score=0.722 total time=   0.0s\n", "[CV 5/5] END colsample_bytree=0.9, learning_rate=0.2, max_depth=3, n_estimators=100, subsample=1.0;, score=0.718 total time=   0.0s\n", "[CV 1/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=1.0;, score=0.718 total time=   0.0s\n", "[CV 2/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=1.0;, score=0.722 total time=   0.0s\n", "[CV 3/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=1.0;, score=0.715 total time=   0.0s\n", "[CV 4/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=1.0;, score=0.722 total time=   0.0s\n", "[CV 5/5] END colsample_bytree=0.8, learning_rate=0.2, max_depth=7, n_estimators=50, subsample=1.0;, score=0.717 total time=   0.0s\n", "[CV 1/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.1s\n", "[CV 3/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.1s\n", "[CV 4/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.2s\n", "[CV 5/5] END colsample_bytree=0.9, learning_rate=0.01, max_depth=7, n_estimators=100, subsample=0.8;, score=0.717 total time=   0.2s\n", "[CV 1/5] END colsample_bytree=0.9, learning_rate=0.1, max_depth=5, n_estimators=100, subsample=0.9;, score=0.721 total time=   0.1s\n", "[CV 2/5] END colsample_bytree=0.9, learning_rate=0.1, max_depth=5, n_estimators=100, subsample=0.9;, score=0.721 total time=   0.1s\n", "[CV 3/5] END colsample_bytree=0.9, learning_rate=0.1, max_depth=5, n_estimators=100, subsample=0.9;, score=0.719 total time=   0.1s\n", "[CV 4/5] END colsample_bytree=0.9, learning_rate=0.1, max_depth=5, n_estimators=100, subsample=0.9;, score=0.723 total time=   0.1s\n", "[CV 5/5] END colsample_bytree=0.9, learning_rate=0.1, max_depth=5, n_estimators=100, subsample=0.9;, score=0.719 total time=   0.1s\n"]}, {"data": {"text/plain": ["RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_delta_step=None, max_depth=None,\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   param_distributions={'colsample_bytree': [0.8, 0.9, 1.0],\n", "                                        'learning_rate': [0.01, 0.1, 0.2],\n", "                                        'max_depth': [3, 5, 7],\n", "                                        'n_estimators': [50, 100, 200],\n", "                                        'subsample': [0.8, 0.9, 1.0]},\n", "                   scoring='accuracy', verbose=3)"], "text/html": ["<style>#sk-container-id-22 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-22 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-22 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-22 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-22 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-22 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-22 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-22 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-22 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-22 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-22 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-22 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-22 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-22 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-22 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-22 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-22 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-22 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-22 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-22 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-22 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-22 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-22 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-22 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-22 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-22 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-22 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-22 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-22 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-22 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-22\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_delta_step=None, max_depth=None,\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   param_distributions={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                                        &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                                        &#x27;max_depth&#x27;: [3, 5, 7],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                                        &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-52\" type=\"checkbox\" ><label for=\"sk-estimator-id-52\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomizedSearchCV<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.model_selection.RandomizedSearchCV.html\">?<span>Documentation for RandomizedSearchCV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomizedSearchCV(cv=5,\n", "                   estimator=XGBClassifier(base_score=None, booster=None,\n", "                                           callbacks=None,\n", "                                           colsample_bylevel=None,\n", "                                           colsample_bynode=None,\n", "                                           colsample_bytree=None, device=None,\n", "                                           early_stopping_rounds=None,\n", "                                           enable_categorical=False,\n", "                                           eval_metric=None, feature_types=None,\n", "                                           feature_weights=None, gamma=None,\n", "                                           grow_policy=None,\n", "                                           importance_type=None,\n", "                                           interaction_constrain...\n", "                                           max_delta_step=None, max_depth=None,\n", "                                           max_leaves=None,\n", "                                           min_child_weight=None, missing=nan,\n", "                                           monotone_constraints=None,\n", "                                           multi_strategy=None,\n", "                                           n_estimators=200, n_jobs=None,\n", "                                           num_parallel_tree=None, ...),\n", "                   param_distributions={&#x27;colsample_bytree&#x27;: [0.8, 0.9, 1.0],\n", "                                        &#x27;learning_rate&#x27;: [0.01, 0.1, 0.2],\n", "                                        &#x27;max_depth&#x27;: [3, 5, 7],\n", "                                        &#x27;n_estimators&#x27;: [50, 100, 200],\n", "                                        &#x27;subsample&#x27;: [0.8, 0.9, 1.0]},\n", "                   scoring=&#x27;accuracy&#x27;, verbose=3)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-53\" type=\"checkbox\" ><label for=\"sk-estimator-id-53\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">best_estimator_: XGBClassifier</label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=0.9, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.1, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=5,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=100,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-54\" type=\"checkbox\" ><label for=\"sk-estimator-id-54\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;XGBClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=0.9, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=0.1, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=5,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=100,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div></div></div></div></div></div>"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "execution_count": 47}, {"cell_type": "code", "id": "6eacdae0193638fc", "metadata": {"ExecuteTime": {"end_time": "2025-07-29T17:49:01.039728Z", "start_time": "2025-07-29T17:49:01.032166Z"}}, "source": ["print(f\"Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression:\", random_log_reg.best_params_)\n", "print(f\"Best cross-validation score for Logistic Regression:\", random_log_reg.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest:\", random_rf.best_params_)\n", "print(f\"Best cross-validation score for Random Forest:\", random_rf.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost:\", random_xgh.best_params_)\n", "print(f\"Best cross-validation score for XGBoost:\", random_xgh.best_score_)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------------------\n", "Best parameters for Logistic Regression: {'solver': 'liblinear', 'penalty': 'l1', 'C': 10.0}\n", "Best cross-validation score for Logistic Regression: 0.7162089132884818\n", "\n", "\n", "Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------------\n", "Best parameters for Random Forest: {'n_estimators': 100, 'min_samples_split': 2, 'min_samples_leaf': 2, 'max_depth': 10}\n", "Best cross-validation score for Random Forest: 0.7190230071387231\n", "\n", "\n", "XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\n", "---------------------------------------------------------------\n", "Best parameters for XGBoost: {'subsample': 0.9, 'n_estimators': 100, 'max_depth': 5, 'learning_rate': 0.1, 'colsample_bytree': 0.9}\n", "Best cross-validation score for XGBoost: 0.7204648328529271\n"]}], "execution_count": 48}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}