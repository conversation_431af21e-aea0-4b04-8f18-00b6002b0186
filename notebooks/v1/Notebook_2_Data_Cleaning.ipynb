{"cells": [{"cell_type": "markdown", "id": "58f9d1575354c326", "metadata": {}, "source": ["<h1>2. Data Cleaning</h1>"]}, {"cell_type": "code", "id": "f397eaa90b2253d1", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:58.043130Z", "start_time": "2025-07-28T22:00:58.039650Z"}}, "source": ["import pandas as pd\n", "import os"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "id": "5092c4ab9d45c6c7", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:58.337051Z", "start_time": "2025-07-28T22:00:58.135312Z"}}, "source": ["df = pd.read_csv(\"C:/Research/Msc/CMM709/Project/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show.csv\")\n", "df"], "outputs": [{"data": {"text/plain": ["           PatientId  AppointmentID Gender          ScheduledDay  \\\n", "0       2.987250e+13        5642903      F  2016-04-29T18:38:08Z   \n", "1       5.589978e+14        5642503      M  2016-04-29T16:08:27Z   \n", "2       4.262962e+12        5642549      F  2016-04-29T16:19:04Z   \n", "3       8.679512e+11        5642828      F  2016-04-29T17:29:31Z   \n", "4       8.841186e+12        5642494      F  2016-04-29T16:07:23Z   \n", "...              ...            ...    ...                   ...   \n", "110522  2.572134e+12        5651768      F  2016-05-03T09:15:35Z   \n", "110523  3.596266e+12        5650093      F  2016-05-03T07:27:33Z   \n", "110524  1.557663e+13        5630692      F  2016-04-27T16:03:52Z   \n", "110525  9.213493e+13        5630323      F  2016-04-27T15:09:23Z   \n", "110526  3.775115e+14        5629448      F  2016-04-27T13:30:56Z   \n", "\n", "              AppointmentDay  Age      Neighbourhood  Scholarship  \\\n", "0       2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0   \n", "1       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "2       2016-04-29T00:00:00Z   62      MATA DA PRAIA            0   \n", "3       2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0   \n", "4       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "...                      ...  ...                ...          ...   \n", "110522  2016-06-07T00:00:00Z   56        MARIA ORTIZ            0   \n", "110523  2016-06-07T00:00:00Z   51        MARIA ORTIZ            0   \n", "110524  2016-06-07T00:00:00Z   21        MARIA ORTIZ            0   \n", "110525  2016-06-07T00:00:00Z   38        MARIA ORTIZ            0   \n", "110526  2016-06-07T00:00:00Z   54        MARIA ORTIZ            0   \n", "\n", "        Hipertension  Diabetes  Alcoholism  Handcap  SMS_received No-show  \n", "0                  1         0           0        0             0      No  \n", "1                  0         0           0        0             0      No  \n", "2                  0         0           0        0             0      No  \n", "3                  0         0           0        0             0      No  \n", "4                  1         1           0        0             0      No  \n", "...              ...       ...         ...      ...           ...     ...  \n", "110522             0         0           0        0             1      No  \n", "110523             0         0           0        0             1      No  \n", "110524             0         0           0        0             1      No  \n", "110525             0         0           0        0             1      No  \n", "110526             0         0           0        0             1      No  \n", "\n", "[110527 rows x 14 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2.572134e+12</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T09:15:35Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3.596266e+12</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T07:27:33Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>1.557663e+13</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T16:03:52Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>9.213493e+13</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:09:23Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>3.775115e+14</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T13:30:56Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"cell_type": "code", "id": "44dc80ce", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:58.424563Z", "start_time": "2025-07-28T22:00:58.397327Z"}}, "source": ["temp_df = df.copy(deep=True)\n", "\n", "# Convert to small-caps\n", "temp_df.columns = [col.strip().lower().replace(\"-\", \"\").replace(\" \", \"_\") for col in temp_df.columns]\n", "\n", "# Correct `typo`\n", "temp_df.rename(columns={\n", "    'patientid': 'patient_id',\n", "    'appointmentid': 'appointment_id',\n", "    'scheduledday': 'scheduled_day',\n", "    'appointmentday': 'appointment_day',\n", "    'noshow': 'no_show',\n", "    'hipertension': 'hypertension',\n", "    'handcap': 'handicap',\n", "    'noshow': 'no_show'\n", "}, inplace=True)\n", "\n", "temp_df"], "outputs": [{"data": {"text/plain": ["          patient_id  appointment_id gender         scheduled_day  \\\n", "0       2.987250e+13         5642903      F  2016-04-29T18:38:08Z   \n", "1       5.589978e+14         5642503      M  2016-04-29T16:08:27Z   \n", "2       4.262962e+12         5642549      F  2016-04-29T16:19:04Z   \n", "3       8.679512e+11         5642828      F  2016-04-29T17:29:31Z   \n", "4       8.841186e+12         5642494      F  2016-04-29T16:07:23Z   \n", "...              ...             ...    ...                   ...   \n", "110522  2.572134e+12         5651768      F  2016-05-03T09:15:35Z   \n", "110523  3.596266e+12         5650093      F  2016-05-03T07:27:33Z   \n", "110524  1.557663e+13         5630692      F  2016-04-27T16:03:52Z   \n", "110525  9.213493e+13         5630323      F  2016-04-27T15:09:23Z   \n", "110526  3.775115e+14         5629448      F  2016-04-27T13:30:56Z   \n", "\n", "             appointment_day  age      neighbourhood  scholarship  \\\n", "0       2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0   \n", "1       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "2       2016-04-29T00:00:00Z   62      MATA DA PRAIA            0   \n", "3       2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0   \n", "4       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "...                      ...  ...                ...          ...   \n", "110522  2016-06-07T00:00:00Z   56        MARIA ORTIZ            0   \n", "110523  2016-06-07T00:00:00Z   51        MARIA ORTIZ            0   \n", "110524  2016-06-07T00:00:00Z   21        MARIA ORTIZ            0   \n", "110525  2016-06-07T00:00:00Z   38        MARIA ORTIZ            0   \n", "110526  2016-06-07T00:00:00Z   54        MARIA ORTIZ            0   \n", "\n", "        hypertension  diabetes  alcoholism  handicap  sms_received no_show  \n", "0                  1         0           0         0             0      No  \n", "1                  0         0           0         0             0      No  \n", "2                  0         0           0         0             0      No  \n", "3                  0         0           0         0             0      No  \n", "4                  1         1           0         0             0      No  \n", "...              ...       ...         ...       ...           ...     ...  \n", "110522             0         0           0         0             1      No  \n", "110523             0         0           0         0             1      No  \n", "110524             0         0           0         0             1      No  \n", "110525             0         0           0         0             1      No  \n", "110526             0         0           0         0             1      No  \n", "\n", "[110527 rows x 14 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2.572134e+12</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T09:15:35Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3.596266e+12</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T07:27:33Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>1.557663e+13</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T16:03:52Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>9.213493e+13</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:09:23Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>3.775115e+14</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T13:30:56Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"cell_type": "code", "id": "be2615b1", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:58.879884Z", "start_time": "2025-07-28T22:00:58.555297Z"}}, "source": ["# Drop unnecessary columns\n", "# temp_df = temp_df.drop(temp_df.columns[0], axis=1)  # Drop the first unnamed column\n", "\n", "# Convert label data types\n", "temp_df['patient_id'] = temp_df['patient_id'].astype('int64')\n", "temp_df['appointment_id'] = temp_df['appointment_id'].astype('int64')\n", "\n", "# Convert `gender`, `neighbourhood`, `handicap`, to categorical type\n", "for col in ['gender', 'neighbourhood', 'handicap']:\n", "    temp_df[col] = temp_df[col].astype('category')\n", "    \n", "# Convert `scheduled_day` and `appointment_day` to datetime\n", "for col in ['scheduled_day', 'appointment_day']:\n", "    temp_df[col] = pd.to_datetime(temp_df[col]).dt.date.astype('datetime64[ns]')\n", "\n", "# Convert `scholarship`, `hypertension`, `diabetes`, `alcoholism`, `sms_received` to boolean type\n", "for col in ['scholarship', 'hypertension', 'diabetes', 'alcoholism']:\n", "    temp_df[col] = temp_df[col].astype('int64')\n", "    \n", "# Convert `no_show` to boolean type\n", "temp_df['sms_received'] = temp_df['sms_received'].astype('bool')\n", "temp_df['no_show']  = temp_df['no_show'].replace({'No': 0, 'Yes': 1}).infer_objects(copy=False)\n", "temp_df['no_show'] = temp_df['no_show'].astype('bool')\n", "\n", "# Change `age` -1 value to 0\n", "temp_df.replace({'age':{-1: 0}}, inplace=True)\n", "\n", "temp_df"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20596\\**********.py:22: FutureWarning: Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  temp_df['no_show']  = temp_df['no_show'].replace({'No': 0, 'Yes': 1}).infer_objects(copy=False)\n"]}, {"data": {"text/plain": ["             patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0        29872499824296         5642903      F    2016-04-29      2016-04-29   \n", "1       558997776694438         5642503      M    2016-04-29      2016-04-29   \n", "2         4262962299951         5642549      F    2016-04-29      2016-04-29   \n", "3          867951213174         5642828      F    2016-04-29      2016-04-29   \n", "4         8841186448183         5642494      F    2016-04-29      2016-04-29   \n", "...                 ...             ...    ...           ...             ...   \n", "110522    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "110523    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "110524   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "110525   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "110526  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "        age      neighbourhood  scholarship  hypertension  diabetes  \\\n", "0        62    JARDIM DA PENHA            0             1         0   \n", "1        56    JARDIM DA PENHA            0             0         0   \n", "2        62      MATA DA PRAIA            0             0         0   \n", "3         8  PONTAL DE CAMBURI            0             0         0   \n", "4        56    JARDIM DA PENHA            0             1         1   \n", "...     ...                ...          ...           ...       ...   \n", "110522   56        MARIA ORTIZ            0             0         0   \n", "110523   51        MARIA ORTIZ            0             0         0   \n", "110524   21        MARIA ORTIZ            0             0         0   \n", "110525   38        MARIA ORTIZ            0             0         0   \n", "110526   54        MARIA ORTIZ            0             0         0   \n", "\n", "        alcoholism handicap  sms_received  no_show  \n", "0                0        0         False    False  \n", "1                0        0         False    False  \n", "2                0        0         False    False  \n", "3                0        0         False    False  \n", "4                0        0         False    False  \n", "...            ...      ...           ...      ...  \n", "110522           0        0          True    False  \n", "110523           0        0          True    False  \n", "110524           0        0          True    False  \n", "110525           0        0          True    False  \n", "110526           0        0          True    False  \n", "\n", "[110527 rows x 14 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>29872499824296</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>558997776694438</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4262962299951</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>867951213174</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8841186448183</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"cell_type": "code", "id": "f3ed0cd2", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:59.114055Z", "start_time": "2025-07-28T22:00:59.092611Z"}}, "source": ["# verify the changes\n", "temp_df.info()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 110527 entries, 0 to 110526\n", "Data columns (total 14 columns):\n", " #   Column           Non-Null Count   Dtype         \n", "---  ------           --------------   -----         \n", " 0   patient_id       110527 non-null  int64         \n", " 1   appointment_id   110527 non-null  int64         \n", " 2   gender           110527 non-null  category      \n", " 3   scheduled_day    110527 non-null  datetime64[ns]\n", " 4   appointment_day  110527 non-null  datetime64[ns]\n", " 5   age              110527 non-null  int64         \n", " 6   neighbourhood    110527 non-null  category      \n", " 7   scholarship      110527 non-null  int64         \n", " 8   hypertension     110527 non-null  int64         \n", " 9   diabetes         110527 non-null  int64         \n", " 10  alcoholism       110527 non-null  int64         \n", " 11  handicap         110527 non-null  category      \n", " 12  sms_received     110527 non-null  bool          \n", " 13  no_show          110527 non-null  bool          \n", "dtypes: bool(2), category(3), datetime64[ns](2), int64(7)\n", "memory usage: 8.1 MB\n"]}], "execution_count": 5}, {"cell_type": "code", "id": "5e25fa59", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:00:59.348992Z", "start_time": "2025-07-28T22:00:59.285458Z"}}, "source": ["temp_df.describe(include='all')"], "outputs": [{"data": {"text/plain": ["          patient_id  appointment_id  gender                  scheduled_day  \\\n", "count   1.105270e+05    1.105270e+05  110527                         110527   \n", "unique           NaN             NaN       2                            NaN   \n", "top              NaN             NaN       F                            NaN   \n", "freq             NaN             NaN   71840                            NaN   \n", "mean    1.474963e+14    5.675305e+06     NaN  2016-05-08 20:33:18.179630080   \n", "min     3.921700e+04    5.030230e+06     NaN            2015-11-10 00:00:00   \n", "25%     4.172614e+12    5.640286e+06     NaN            2016-04-29 00:00:00   \n", "50%     3.173184e+13    5.680573e+06     NaN            2016-05-10 00:00:00   \n", "75%     9.439172e+13    5.725524e+06     NaN            2016-05-20 00:00:00   \n", "max     9.999816e+14    5.790484e+06     NaN            2016-06-08 00:00:00   \n", "std     2.560949e+14    7.129575e+04     NaN                            NaN   \n", "\n", "                      appointment_day            age   neighbourhood  \\\n", "count                          110527  110527.000000          110527   \n", "unique                            NaN            NaN              81   \n", "top                               NaN            NaN  JARDIM CAMBURI   \n", "freq                              NaN            NaN            7717   \n", "mean    2016-05-19 00:57:50.008233472      37.088883             NaN   \n", "min               2016-04-29 00:00:00       0.000000             NaN   \n", "25%               2016-05-09 00:00:00      18.000000             NaN   \n", "50%               2016-05-18 00:00:00      37.000000             NaN   \n", "75%               2016-05-31 00:00:00      55.000000             NaN   \n", "max               2016-06-08 00:00:00     115.000000             NaN   \n", "std                               NaN      23.110190             NaN   \n", "\n", "          scholarship   hypertension       diabetes     alcoholism  handicap  \\\n", "count   110527.000000  110527.000000  110527.000000  110527.000000  110527.0   \n", "unique            NaN            NaN            NaN            NaN       5.0   \n", "top               NaN            NaN            NaN            NaN       0.0   \n", "freq              NaN            NaN            NaN            NaN  108286.0   \n", "mean         0.098266       0.197246       0.071865       0.030400       NaN   \n", "min          0.000000       0.000000       0.000000       0.000000       NaN   \n", "25%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "50%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "75%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "max          1.000000       1.000000       1.000000       1.000000       NaN   \n", "std          0.297675       0.397921       0.258265       0.171686       NaN   \n", "\n", "       sms_received no_show  \n", "count        110527  110527  \n", "unique            2       2  \n", "top           False   False  \n", "freq          75045   88208  \n", "mean            NaN     NaN  \n", "min             <PERSON><PERSON>  \n", "25%             NaN     NaN  \n", "50%             NaN     NaN  \n", "75%             NaN     NaN  \n", "max             NaN     NaN  \n", "std             NaN     NaN  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.0</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>F</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>JARDIM CAMBURI</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>71840</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7717</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>108286.0</td>\n", "      <td>75045</td>\n", "      <td>88208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-08 20:33:18.179630080</td>\n", "      <td>2016-05-19 00:57:50.008233472</td>\n", "      <td>37.088883</td>\n", "      <td>NaN</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921700e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>NaN</td>\n", "      <td>2015-11-10 00:00:00</td>\n", "      <td>2016-04-29 00:00:00</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-04-29 00:00:00</td>\n", "      <td>2016-05-09 00:00:00</td>\n", "      <td>18.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-10 00:00:00</td>\n", "      <td>2016-05-18 00:00:00</td>\n", "      <td>37.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-20 00:00:00</td>\n", "      <td>2016-05-31 00:00:00</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-06-08 00:00:00</td>\n", "      <td>2016-06-08 00:00:00</td>\n", "      <td>115.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>23.110190</td>\n", "      <td>NaN</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"cell_type": "code", "id": "f32835bdaff67679", "metadata": {"ExecuteTime": {"end_time": "2025-07-28T22:01:00.072157Z", "start_time": "2025-07-28T22:00:59.457494Z"}}, "source": ["FILE_PATH = r\"C:/Research/Msc/CMM709/Project/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show_cleaned.csv\"\n", "\n", "# Delete existing file if it exists\n", "if os.path.exists(FILE_PATH):\n", "    os.remove(FILE_PATH)\n", "\n", "# Save the cleaned DataFrame to a CSV file\n", "temp_df.to_csv(FILE_PATH, index=False)"], "outputs": [], "execution_count": 7}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}