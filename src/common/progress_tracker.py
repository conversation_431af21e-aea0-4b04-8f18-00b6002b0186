"""
Progress tracking utilities for model training.
"""
import time
import logging
from typing import Optional, Dict, Any
from tqdm import tqdm


class TrainingProgressTracker:
    """
    A utility class to track and display training progress with detailed metrics.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.start_time = None
        self.current_step = 0
        self.total_steps = 0
        self.step_times = []
        self.progress_bar = None
        
    def start_training(self, total_steps: int, description: str = "Training"):
        """Start tracking training progress."""
        self.start_time = time.time()
        self.total_steps = total_steps
        self.current_step = 0
        self.step_times = []
        
        self.logger.info(f"Starting {description} with {total_steps} steps...")
        self.progress_bar = tqdm(
            total=total_steps,
            desc=description,
            unit="step",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]"
        )
        
    def update_step(self, step_name: str = "", metrics: Optional[Dict[str, Any]] = None):
        """Update progress for current step."""
        if self.progress_bar is None:
            return
            
        step_start = time.time()
        self.current_step += 1
        
        # Update progress bar
        self.progress_bar.update(1)
        
        # Update description and metrics
        desc = f"Step {self.current_step}/{self.total_steps}"
        if step_name:
            desc += f" - {step_name}"
            
        self.progress_bar.set_description(desc)
        
        if metrics:
            self.progress_bar.set_postfix(metrics)
            
        # Log step completion
        step_time = time.time() - step_start
        self.step_times.append(step_time)
        
        if step_name:
            self.logger.info(f"Completed: {step_name}")
            
    def finish_training(self, final_metrics: Optional[Dict[str, Any]] = None):
        """Finish training and display summary."""
        if self.progress_bar:
            self.progress_bar.close()
            
        total_time = time.time() - self.start_time if self.start_time else 0
        avg_step_time = sum(self.step_times) / len(self.step_times) if self.step_times else 0
        
        self.logger.info(f"Training completed in {total_time:.2f}s")
        self.logger.info(f"Average step time: {avg_step_time:.2f}s")
        
        if final_metrics:
            self.logger.info("Final metrics:")
            for key, value in final_metrics.items():
                self.logger.info(f"  {key}: {value}")


class ModelComparisonTracker:
    """
    Track progress when training multiple models for comparison.
    """
    
    def __init__(self, models: list, logger: Optional[logging.Logger] = None):
        self.models = models
        self.logger = logger or logging.getLogger(__name__)
        self.results = []
        self.current_model_idx = 0
        self.start_time = None
        
    def start_comparison(self):
        """Start model comparison tracking."""
        self.start_time = time.time()
        self.results = []
        self.current_model_idx = 0
        
        self.logger.info(f"Starting model comparison with {len(self.models)} models")
        self.logger.info("=" * 60)
        
    def start_model_training(self, model_name: str):
        """Start training a specific model."""
        self.current_model_idx += 1
        
        self.logger.info(f"Training Model {self.current_model_idx}/{len(self.models)}: {model_name}")
        self.logger.info("-" * 40)
        
        return time.time()
        
    def finish_model_training(self, model_name: str, start_time: float, 
                            success: bool = True, metrics: Optional[Dict] = None, 
                            error: Optional[str] = None):
        """Finish training a specific model."""
        training_time = time.time() - start_time
        
        result = {
            'model_name': model_name,
            'training_time': training_time,
            'success': success
        }
        
        if success and metrics:
            result['metrics'] = metrics
            self.logger.info(f"✓ {model_name} completed successfully in {training_time:.2f}s")
            if 'accuracy' in metrics:
                self.logger.info(f"  Accuracy: {metrics['accuracy']:.4f}")
        elif error:
            result['error'] = error
            self.logger.error(f"✗ {model_name} failed after {training_time:.2f}s: {error}")
            
        self.results.append(result)
        
        # Show progress
        progress = (self.current_model_idx / len(self.models)) * 100
        self.logger.info(f"Progress: {progress:.1f}% ({self.current_model_idx}/{len(self.models)})")
        self.logger.info("")
        
    def finish_comparison(self):
        """Finish model comparison and show summary."""
        total_time = time.time() - self.start_time if self.start_time else 0
        successful_models = [r for r in self.results if r['success']]
        
        self.logger.info("=" * 60)
        self.logger.info("MODEL COMPARISON SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total time: {total_time:.2f}s")
        self.logger.info(f"Successful models: {len(successful_models)}/{len(self.models)}")
        self.logger.info("")
        
        # Sort by accuracy if available
        if successful_models and all('metrics' in r and 'accuracy' in r['metrics'] for r in successful_models):
            successful_models.sort(key=lambda x: x['metrics']['accuracy'], reverse=True)
            self.logger.info("Models ranked by accuracy:")
            for i, result in enumerate(successful_models, 1):
                accuracy = result['metrics']['accuracy']
                time_taken = result['training_time']
                self.logger.info(f"  {i}. {result['model_name']}: {accuracy:.4f} ({time_taken:.2f}s)")
                
        return self.results


def create_progress_logger(name: str = "training", level: int = logging.INFO) -> logging.Logger:
    """Create a logger optimized for progress tracking."""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create console handler with custom format
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger
