from typing import Any, Dict, Optional
from datetime import datetime
import sys
import os
import joblib
import json
import pickle
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from globals.basic_settings import config

def save_model(model: Any, model_path: str, metadata: Optional[Dict] = None, use_joblib: bool = True):
    """
    Save a model to a file with optional metadata.
    Args:
        model (Any): The model to save.
        model_path (str): The path where the model will be saved.
        metadata (Optional[Dict]): Optional metadata to save alongside the model.
        use_joblib (bool): Whether to use joblib for saving the model.
    """
    try:
        # Create a directory if it doesn't exist
        os.makedirs(os.path.dirname(model_path), exist_ok=True)

        # Save the model
        if use_joblib:
            joblib.dump(model, model_path)
        else:
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)

        # Save metadata if provided
        if metadata:
            metadata_path = model_path.replace('.pkl', '_metadata.json')
            metadata['saved_at'] = datetime.now().isoformat()
            metadata['model_path'] = os.path.basename(model_path)

            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)

            print(f"Model and metadata saved successfully at {model_path} and {metadata_path}.")
        else:
            print(f"Model saved successfully at {model_path}. No metadata provided.")
    except Exception as e:
        print(f"Error saving model: {e}")
        raise

def load_model(model_path: str, use_joblib: bool = True) -> Any:
    """
    Load a saved model.
    Args:
        model_path (str): The path from which to load the model.
        use_joblib (bool): Whether to use joblib for loading the model.
    Returns:
        The loaded model.
    """
    try:
        if use_joblib:
            model = joblib.load(model_path)
        else:
            with open(model_path, 'rb') as f:
                model = pickle.load(f)

        print(f"Model loaded successfully from {model_path}.")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        raise

def load_metadata(model_path: str) -> Optional[Dict]:
    """
    Load metadata associated with a saved model.
    Args:
        model_path (str): The path from which to load the metadata.
    Returns:
        Optional[Dict]: The loaded metadata, or None if no metadata is found.
    """
    try:
        metadata_path = model_path.replace('.pkl', '_metadata.json')
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            print(f"Metadata loaded successfully from {metadata_path}.")
            return metadata
        else:
            print(f"No metadata found at {metadata_path}.")
            return None
    except Exception as e:
        print(f"Error loading metadata: {e}")
        return None

def get_model_save_path(model_name: str, model_type: str, use_user_dir: bool = True) -> str:
    """
    Get a safe model save path using basic_settings configuration.
    Args:
        model_name (str): Name of the model file (without extension)
        model_type (str): Type of the model ('predictive', 'causal') points to different directories
        use_user_dir (bool): Whether to use user directory instead of project directory
    Returns:
        str: Full path to save the model
    """
    if use_user_dir:
        # Use user home directory to avoid permission issues
        user_models_dir = Path.home() / 'causality_models'
        user_models_dir.mkdir(exist_ok=True)
        return str(user_models_dir / f"{model_type}_{model_name}.pkl")
    else:
        # Try to use configured model path from basic_settings
        try:
            model_path = config.data_paths.get_model_file(model_type, f"{model_type}_{model_name}.pkl")
            # Test if we can write to this directory
            os.makedirs(model_path.parent, exist_ok=True)
            # Test write access
            test_file = model_path.parent / 'test_write.tmp'
            test_file.touch()
            test_file.unlink()
            return str(model_path)
        except (PermissionError, OSError) as e:
            print(f"Cannot write to configured model directory: {e}")
            print("Falling back to user directory...")
            return get_model_save_path(model_name, use_user_dir=True)

def get_data_file_path(filename: str, stage: str = 'raw') -> Path:
    """
    Get data file path using basic_settings configuration.
    Args:
        filename (str): Name of the data file
        stage (str): Data processing stage ('raw', 'interim', 'processed', 'final')
    Returns:
        Path: Full path to the data file
    """
    return config.data_paths.get_data_file(filename, stage)

def get_model_file_path(filename: str, model_type: str) -> Path:
    """
    Get model file path using basic_settings configuration.
    Args:
        filename (str): Name of the model file
        model_type (str): Type of the model ('predictive', 'causal') points to different directories
    Returns:
        Path: Full path to the model file
    """
    return config.data_paths.get_model_file(model_type,filename)