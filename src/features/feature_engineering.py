import os
import sys
import logging
import pandas as pd
from pathlib import Path
from typing import Optional, Union

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from globals.basic_settings import config
import data.load_data as ld

# Configure logging properly
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FeatureEngineering:
    """A class for feature engineering on medical appointment data."""

    def __init__(self, data: Optional[pd.DataFrame] = None):
        """
        Initialize the FeatureEngineering class.

        Args:
            data: Optional DataFrame. If None, loads preprocessed data.
        """
        self.data = None
        self.original_data = None
        self.feature_data = None

        if data is not None:
            self.data = data.copy(deep=True)
            self.original_data = data.copy(deep=True)

    def load_data(self) -> 'FeatureEngineering':
        """
        Load preprocessed data from file.

        Returns:
            self: For method chaining.
        """
        try:
            self.data = ld.load_preprocessed_data()
            self.original_data = self.data.copy(deep=True)
            logger.info(f"Data loaded successfully: {self.data.shape[0]} rows, {self.data.shape[1]} columns")
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise

        return self

    def validate_data(self) -> 'FeatureEngineering':
        """
        Validate that required columns exist.

        Returns:
            self: For method chaining
        """
        if self.data is None or self.data.empty:
            raise ValueError("No data loaded. Call load_data() first.")

        required_columns = [
            'patient_id', 'appointment_id', 'age', 'gender',
            'scheduled_day', 'appointment_day', 'neighbourhood',
            'scholarship', 'hypertension', 'diabetes', 'alcoholism',
            'handicap', 'sms_received', 'no_show'
        ]

        if missing_columns := [
            col for col in required_columns if col not in self.data.columns
        ]:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

        logger.info("Data validation passed")
        return self

    def prepare_data_types(self) -> 'FeatureEngineering':
        """
        Ensure correct data types for feature engineering.

        Returns:
            self: For method chaining
        """
        try:
            self._extracted_from_prepare_data_types()
        except Exception as e:
            logger.error(f"Error preparing data types: {e}")
            raise

        return self

    def _extracted_from_prepare_data_types(self):
        # Convert datetime columns - FIXED: removed nested loop
        datetime_cols = ['appointment_day', 'scheduled_day']
        for col in datetime_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_datetime(self.data[col], format='mixed', errors='coerce')

        # Ensure numeric columns
        numeric_cols = [
            'age', 'scholarship', 'hypertension', 'diabetes', 'alcoholism', 'handicap'
        ]
        for col in numeric_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_numeric(self.data[col], errors='coerce')

        # Ensure no_show is binary
        if 'no_show' in self.data.columns:
            if self.data['no_show'].dtype == 'object':
                self.data['no_show'] = self.data['no_show'].map({'No': 0, 'Yes': 1})
            self.data['no_show'] = self.data['no_show'].astype('int64')

        logger.info("Data types prepared successfully")

    def create_features(self) -> 'FeatureEngineering':
        """
        Create new features from the existing data.

        Returns:
            self: For method chaining
        """
        try:
            self._extracted_from_create_features()
        except Exception as e:
            logger.error(f"Error creating features: {e}")
            raise

        return self

    def _extracted_from_create_features(self):
        # Work with a copy
        df = self.data.copy(deep=True)

        # a. Lead time (days between scheduling and appointment)
        df['lead_time'] = (df['appointment_day'] - df['scheduled_day']).dt.days
        df['lead_time'] = df['lead_time'].clip(lower=0)  # Ensure non-negative

        # b. Scheduled hour
        df['scheduled_hour'] = df['scheduled_day'].dt.hour

        # c. Appointment day features
        df['appointment_day_of_week'] = df[
            'appointment_day'].dt.day_name()  # FIXED: changed from appointment_day_weekday
        df['appointment_month'] = df['appointment_day'].dt.month
        df['appointment_day_num'] = df['appointment_day'].dt.day  # FIXED: renamed to avoid overwriting datetime column

        # d. Weekend indicator
        df['is_weekend'] = df['appointment_day_of_week'].isin(['Saturday', 'Sunday']).astype(int)

        # e. Age groups
        df['age_group'] = pd.cut(
            df['age'],
            bins=[0, 18, 35, 50, 65, 100],
            labels=['0-18', '19-35', '36-50', '51-65', '66+']
        )

        # f. Patient history features (sort by date first)
        df = df.sort_values(['patient_id', 'scheduled_day'])

        # Previous no-show for this patient
        df['previous_no_shows'] = df.groupby('patient_id')['no_show'].cumsum() - df['no_show']

        # Total appointments for this patient
        df['total_appointments'] = df.groupby('patient_id')['appointment_id'].transform('count')

        # No-show rate for this patient (excluding current appointment)
        df['patient_no_show_rate'] = df['previous_no_shows'] / df.groupby(
            'patient_id').cumcount()  # FIXED: changed from previous_no_show
        df['patient_no_show_rate'] = df['patient_no_show_rate'].fillna(0)

        # g. Neighbourhood features
        # Calculate neighbourhood no-show rate
        neighbourhood_stats = df.groupby('neighbourhood')['no_show'].agg(['mean', 'count'])
        neighbourhood_stats.columns = ['neighbourhood_no_show_rate', 'neighbourhood_appointments']

        # Keep only top 20 neighbourhoods by appointments count
        top_neighbourhoods = neighbourhood_stats.nlargest(20, 'neighbourhood_appointments').index
        df['neighbourhood_group'] = df['neighbourhood'].apply(
            lambda x: x if x in top_neighbourhoods else 'Other'
        )

        # h. Health conditions features
        df['chronic_conditions'] = (
                df['hypertension'] + df['diabetes'] + df['alcoholism']
        )
        df['has_chronic_condition'] = (df['chronic_conditions'] > 0).astype('int64')

        # i. SMS received feature
        df['sms_sent'] = df['sms_received'].astype('int64')

        # Store the feature-engineered data
        self.feature_data = df

        logger.info(f"Features created successfully. Shape: {df.shape}")
        logger.info(f"New features: {set(df.columns) - set(self.data.columns)}")

    def encode_categorical_features(self) -> 'FeatureEngineering':
        """
        Encode categorical features for machine learning.

        Returns:
            self: For method chaining
        """
        if self.feature_data is None:
            raise ValueError("No feature data available. Call create_features() first.")

        try:
            self._extracted_from_encode_categorical_features()
        except Exception as e:
            logger.error(f"Error encoding categorical features: {e}")
            raise

        return self

    def _extracted_from_encode_categorical_features(self):
        df = self.feature_data.copy(deep=True)

        # One-hot encode categorical features
        categorical_cols = ['gender', 'appointment_day_of_week', 'age_group', 'neighbourhood_group']

        for col in categorical_cols:
            if col in df.columns:
                # Create dummies
                dummies = pd.get_dummies(df[col], prefix=col, drop_first=True)
                df = pd.concat([df, dummies], axis=1)
                # Drop original column
                df = df.drop(columns=[col])
            else:
                logger.warning(f"Categorical column '{col}' not found in data")

        # Drop columns not needed for modeling
        columns_to_drop = ['patient_id', 'appointment_id', 'scheduled_day',
                           'neighbourhood', 'appointment_day', 'appointment_day_num']
        df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

        self.feature_data = df
        logger.info(f"Categorical features encoded. Final shape: {df.shape}")

    def get_feature_summary(self) -> dict:
        """
        Get a summary of the feature engineering process.

        Returns:
            dict: Summary statistics
        """
        if self.feature_data is None:
            return {"error": "No feature data available."}

        return {
            "original_shape": self.original_data.shape if self.original_data is not None else None,
            "final_shape": self.feature_data.shape,
            "features_added": len(set(self.feature_data.columns) - set(
                self.original_data.columns)) if self.original_data is not None else None,
            "features_removed": len(set(self.original_data.columns) - set(
                self.feature_data.columns)) if self.original_data is not None else None,
            "total_features": len(self.feature_data.columns),
            "numeric_features": len(self.feature_data.select_dtypes(include=['int64', 'float64']).columns),
            "categorical_features": len(self.feature_data.select_dtypes(include=['object', 'category']).columns),
            "memory_usage_mb": self.feature_data.memory_usage(deep=True).sum() / (1024 ** 2)
        }

    def save_features(self, filename: Optional[str] = None) -> Path:
        """
        Save the feature-engineered data to file.

        Args:
            filename: Optional filename. If None, uses default from config.

        Returns:
            Path: Path to saved file
        """
        if self.feature_data is None:
            raise ValueError("No feature data available. Call create_features() first.")

        try:
            return self._extracted_from_save_features(filename)
        except Exception as e:
            logger.error(f"Error saving feature data: {e}")
            raise

    def _extracted_from_save_features(self, filename):
        if filename is None:
            filename = config.get('FEATURE_DATA_FILE', 'medical_appointment_no_show_features.csv')

        save_path = ld.save_data(self.feature_data, filename, 'processed')

        # Log summary
        summary = self.get_feature_summary()
        logger.info("Feature Engineering Summary:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")

        return save_path

    def get_data(self) -> pd.DataFrame:
        """
        Get the feature-engineered data.

        Returns:
            pd.DataFrame: Feature-engineered data
        """
        if self.feature_data is None:
            raise ValueError("No feature data available. Call create_features() first.")

        return self.feature_data.copy(deep=True)


def main():
    """Main function to run the feature engineering pipeline."""
    try:
        # Initialize and run pipeline
        feature_eng = FeatureEngineering()

        # Run the pipeline with method chaining
        feature_eng = (feature_eng
                       .load_data()
                       .validate_data()
                       .prepare_data_types()
                       .create_features()
                       .encode_categorical_features())

        # Save the features
        saved_path = feature_eng.save_features()

        # Get summary
        summary = feature_eng.get_feature_summary()

        logger.info("Feature engineering completed successfully!")
        logger.info(f"Features saved to: {saved_path}")

        return feature_eng.get_data()

    except Exception as e:
        logger.error(f"Feature engineering pipeline failed: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    try:
        feature_data = main()
        print(f"\nFeature engineering completed successfully!")
        print(f"Final data shape: {feature_data.shape}")
        print(f"\nFeature columns ({len(feature_data.columns)}):")
        for i, col in enumerate(feature_data.columns, 1):
            print(f"  {i:2d}. {col}")

    except Exception as e:
        print(f"An error occurred: {e}")