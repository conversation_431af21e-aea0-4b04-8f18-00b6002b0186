import os
import sys
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Any, Tu<PERSON>, Optional, List
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.impute import SimpleImputer

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.utils import load_model, load_metadata
from data.load_data import load_app_feature_data
from globals.basic_settings import config


class CausalModelEvaluator:
    """
    Comprehensive evaluator for causal inference models.

    Provides methods to evaluate causal models including:
    - Basic causal metrics (ATE, CATE)
    - Treatment effect analysis
    - Model diagnostics and assumptions
    - Robustness checks
    """

    def __init__(self, model_path: str, data: Optional[pd.DataFrame] = None):
        """
        Initialize the causal model evaluator.

        Args:
            model_path: Path to the trained causal model
            data: Optional evaluation data. If None, loads default data.
        """
        self.model_path = Path(model_path)
        self.logger = self._setup_logging()

        # Load model and metadata
        self.model = self._load_model()
        self.metadata = self._load_metadata()

        # Load or use provided data
        self.data = data if data is not None else load_app_feature_data()

        # Prepare evaluation data
        self.X_test, self.T_test, self.Y_test = self._prepare_evaluation_data()

        self.logger.info(f"Initialized evaluator with {len(self.X_test)} samples")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def _load_model(self):
        """Load the causal model."""
        try:
            model = load_model(str(self.model_path))
            self.logger.info(f"Loaded causal model from {self.model_path}")
            return model
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    def _load_metadata(self) -> Dict[str, Any]:
        """Load model metadata."""
        try:
            metadata = load_metadata(str(self.model_path))
            self.logger.info("Loaded model metadata")
            return metadata
        except Exception as e:
            self.logger.warning(f"Could not load metadata: {e}")
            return {}

    def _prepare_evaluation_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series]:
        """
        Prepare data for evaluation with proper feature alignment.

        Returns:
            Tuple of (features, treatment, outcome)
        """
        try:
            # Get column names
            treatment_col = self.metadata.get('treatment_column', 'sms_sent')
            outcome_col = self.metadata.get('outcome_column', 'no_show')

            # Extract treatment and outcome
            T = self.data[treatment_col].copy()
            Y = self.data[outcome_col].copy()

            # Prepare features with dimension matching
            X = self._align_features_with_model()

            self.logger.info(f"Prepared data: {X.shape[0]} samples, {X.shape[1]} features")
            return X, T, Y

        except Exception as e:
            self.logger.error(f"Error preparing evaluation data: {e}")
            raise

    def _align_features_with_model(self) -> pd.DataFrame:
        """
        Align features with the trained model's expected input.

        Returns:
            DataFrame with properly aligned features
        """
        # Get feature columns (exclude treatment and outcome)
        treatment_col = self.metadata.get('treatment_column', 'sms_sent')
        outcome_col = self.metadata.get('outcome_column', 'no_show')

        feature_cols = [col for col in self.data.columns
                        if col not in [treatment_col, outcome_col]]

        X_candidate = self.data[feature_cols].copy()

        # Handle missing values
        imputer = SimpleImputer(strategy='mean')
        X_imputed = pd.DataFrame(
            imputer.fit_transform(X_candidate),
            columns=X_candidate.columns,
            index=X_candidate.index
        )

        # Test compatibility with model
        return self._find_compatible_features(X_imputed)

    def _find_compatible_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Find feature subset compatible with the trained model.

        Args:
            X: Full feature DataFrame

        Returns:
            Compatible feature DataFrame
        """
        # Try original features first
        if self._test_model_compatibility(X):
            self.logger.info(f"Using all {X.shape[1]} features")
            return X

        # Try different feature counts
        feature_counts = [20, 25, 30, 35, 40, 45, 50]

        for n_features in feature_counts:
            if n_features <= X.shape[1]:
                X_subset = X.iloc[:, :n_features]
                if self._test_model_compatibility(X_subset):
                    self.logger.info(f"Using {n_features} features for compatibility")
                    return X_subset

        raise ValueError("Could not find compatible feature set for model")

    def _test_model_compatibility(self, X: pd.DataFrame) -> bool:
        """
        Test if features are compatible with the model.

        Args:
            X: Feature DataFrame to test

        Returns:
            True if compatible, False otherwise
        """
        try:
            # Test with a small sample
            test_sample = X.iloc[:min(5, len(X))]
            _ = self.model.effect(test_sample)
            return True
        except Exception:
            return False

    def evaluate_comprehensive(self) -> Dict[str, Any]:
        """
        Run a comprehensive evaluation of the causal model.

        Returns:
            Dictionary containing all evaluation results
        """
        results = {}

        # Basic causal metrics
        results['basic_metrics'] = self._safe_evaluate(
            self._calculate_basic_metrics, "basic metrics"
        )

        # Treatment effect analysis
        results['treatment_effects'] = self._safe_evaluate(
            self._analyze_treatment_effects, "treatment effects"
        )

        # Heterogeneity analysis
        results['heterogeneity'] = self._safe_evaluate(
            self._analyze_heterogeneity, "heterogeneity"
        )

        # Model diagnostics
        results['diagnostics'] = self._safe_evaluate(
            self._run_diagnostics, "diagnostics"
        )

        # Causal validity assessment
        results['causal_validity'] = self._safe_evaluate(
            self._assess_causal_validity, "causal validity"
        )

        # First-stage evaluation
        results['first_stage_evaluation'] = self._safe_evaluate(
            self._evaluate_first_stage, "first stage evaluation"
        )

        # Robustness checks
        results['robustness'] = self._safe_evaluate(
            self._run_robustness_checks, "robustness checks"
        )

        return results

    def _safe_evaluate(self, func, description: str) -> Dict[str, Any]:
        """
        Safely execute evaluation function with error handling.

        Args:
            func: Function to execute
            description: Description for logging

        Returns:
            Function result or error information
        """
        try:
            return func()
        except Exception as e:
            self.logger.error(f"Error {description}: {e}")
            return {'error': str(e), 'status': 'failed'}

    def _calculate_basic_metrics(self) -> Dict[str, Any]:
        """Calculate basic causal metrics."""
        # Calculate treatment effects
        treatment_effects = self.model.effect(self.X_test)

        # Average Treatment Effect (ATE)
        ate = np.mean(treatment_effects)
        ate_std = np.std(treatment_effects)

        # Treatment effect statistics
        metrics = {
            'average_treatment_effect': float(ate),
            'ate_std': float(ate_std),
            'ate_confidence_interval': [
                float(ate - 1.96 * ate_std / np.sqrt(len(treatment_effects))),
                float(ate + 1.96 * ate_std / np.sqrt(len(treatment_effects)))
            ],
            'treatment_effect_range': [float(np.min(treatment_effects)), float(np.max(treatment_effects))],
            'n_observations': len(self.X_test),
            'treatment_prevalence': float(self.T_test.mean())
        }

        return metrics

    def _analyze_treatment_effects(self) -> Dict[str, Any]:
        """Analyze treatment effects distribution and patterns."""
        treatment_effects = self.model.effect(self.X_test)

        analysis = {
            'effect_distribution': {
                'mean': float(np.mean(treatment_effects)),
                'median': float(np.median(treatment_effects)),
                'std': float(np.std(treatment_effects)),
                'skewness': float(self._calculate_skewness(treatment_effects)),
                'kurtosis': float(self._calculate_kurtosis(treatment_effects))
            },
            'effect_percentiles': {
                f'p{p}': float(np.percentile(treatment_effects, p))
                for p in [5, 10, 25, 50, 75, 90, 95]
            },
            'positive_effects_ratio': float(np.mean(treatment_effects > 0)),
            'significant_effects_ratio': float(np.mean(np.abs(treatment_effects) > 0.1))
        }

        return analysis

    def _analyze_heterogeneity(self) -> Dict[str, Any]:
        """Analyze treatment effect heterogeneity."""
        treatment_effects = self.model.effect(self.X_test)

        # Calculate heterogeneity metrics
        effect_variance = np.var(treatment_effects)
        effect_range = np.max(treatment_effects) - np.min(treatment_effects)

        # Quartile analysis
        quartiles = np.percentile(treatment_effects, [25, 50, 75])
        iqr = quartiles[2] - quartiles[0]

        heterogeneity = {
            'effect_variance': float(effect_variance),
            'effect_range': float(effect_range),
            'interquartile_range': float(iqr),
            'coefficient_of_variation': float(np.std(treatment_effects) / abs(np.mean(treatment_effects)))
            if np.mean(treatment_effects) != 0 else float('inf'),
            'heterogeneity_assessment': self._assess_heterogeneity_level(effect_variance, iqr)
        }

        return heterogeneity

    def _run_diagnostics(self) -> Dict[str, Any]:
        """Run model diagnostics."""
        diagnostics = {
            'model_assumptions': self._check_model_assumptions(),
            'prediction_quality': self._assess_prediction_quality(),
            'common_support': self._check_common_support()
        }

        return diagnostics

    def _assess_causal_validity(self) -> Dict[str, Any]:
        """Assess causal validity of the model."""
        validity = {
            'balance_assessment': self._assess_balance(),
            'overlap_assessment': self._check_common_support(),
            'confounding_assessment': self._assess_confounding_risk()
        }

        return validity

    def _evaluate_first_stage(self) -> Dict[str, Any]:
        """Evaluate first-stage models performance."""
        first_stage = {
            'treatment_model': self._evaluate_treatment_model(),
            'outcome_model': self._evaluate_outcome_model()
        }

        return first_stage

    def _run_robustness_checks(self) -> Dict[str, Any]:
        """Run robustness checks."""
        robustness = {
            'stability_analysis': self._run_stability_analysis(),
            'sensitivity_analysis': self._run_sensitivity_analysis(),
            'placebo_tests': self._run_placebo_tests()
        }

        return robustness

    # Helper methods for calculations
    def _calculate_skewness(self, data: np.ndarray) -> float:
        """Calculate skewness of data."""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 3)

    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """Calculate kurtosis of data."""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 4) - 3

    def _assess_heterogeneity_level(self, variance: float, iqr: float) -> str:
        """Assess level of treatment effect heterogeneity."""
        if variance < 0.01 and iqr < 0.1:
            return "Low"
        elif variance < 0.05 and iqr < 0.2:
            return "Moderate"
        else:
            return "High"

    def _check_model_assumptions(self) -> Dict[str, Any]:
        """Check key causal model assumptions."""
        return {
            'linearity_check': {'status': 'not_implemented'},
            'independence_check': self._check_independence(),
            'no_unmeasured_confounding': {'assessment': 'cannot_verify_directly'}
        }

    def _check_independence(self) -> Dict[str, Any]:
        """Check treatment assignment independence."""
        correlations = []
        for col in self.X_test.columns:
            corr = np.corrcoef(self.X_test[col], self.T_test)[0, 1]
            if not np.isnan(corr):
                correlations.append(abs(corr))

        max_correlation = max(correlations) if correlations else 0
        return {
            'max_feature_treatment_correlation': float(max_correlation),
            'independence_concern': max_correlation > 0.3
        }

    def _assess_prediction_quality(self) -> Dict[str, Any]:
        """Assess quality of model predictions."""
        return {
            'treatment_prediction': self._evaluate_treatment_model(),
            'outcome_prediction': self._evaluate_outcome_model()
        }

    def _evaluate_treatment_model(self) -> Dict[str, Any]:
        """Evaluate treatment model."""
        try:
            if hasattr(self.model, 'models_t') and self.model.models_t:
                return {'status': 'model_accessible', 'details': 'evaluation_not_implemented'}
            return {'status': 'model_not_accessible'}
        except Exception as e:
            return {'error': str(e)}

    def _evaluate_outcome_model(self) -> Dict[str, Any]:
        """Evaluate outcome model."""
        try:
            if hasattr(self.model, 'models_y') and self.model.models_y:
                return {'status': 'model_accessible', 'details': 'evaluation_not_implemented'}
            return {'status': 'model_not_accessible'}
        except Exception as e:
            return {'error': str(e)}

    def _check_common_support(self) -> Dict[str, Any]:
        """Check common support assumption."""
        treated_mask = self.T_test == 1
        control_mask = self.T_test == 0

        if np.sum(treated_mask) == 0 or np.sum(control_mask) == 0:
            return {'adequate_support': False, 'reason': 'no_variation_in_treatment'}

        return {
            'adequate_support': True,
            'treated_count': int(np.sum(treated_mask)),
            'control_count': int(np.sum(control_mask))
        }

    def _assess_balance(self) -> Dict[str, Any]:
        """Assess covariate balance."""
        treated_mask = self.T_test == 1
        control_mask = self.T_test == 0

        if np.sum(treated_mask) == 0 or np.sum(control_mask) == 0:
            return {'balanced': False, 'reason': 'no_variation_in_treatment'}

        balance_stats = []
        for col in self.X_test.columns:
            treated_mean = np.mean(self.X_test.loc[treated_mask, col])
            control_mean = np.mean(self.X_test.loc[control_mask, col])
            pooled_std = np.sqrt((np.var(self.X_test.loc[treated_mask, col]) +
                                  np.var(self.X_test.loc[control_mask, col])) / 2)

            if pooled_std > 0:
                standardized_diff = abs(treated_mean - control_mean) / pooled_std
                balance_stats.append(standardized_diff)

        if balance_stats:
            mean_imbalance = np.mean(balance_stats)
            return {
                'balanced': mean_imbalance < 0.1,
                'mean_standardized_difference': float(mean_imbalance)
            }

        return {'balanced': False, 'reason': 'could_not_calculate'}

    def _assess_confounding_risk(self) -> Dict[str, Any]:
        """Assess risk of unmeasured confounding."""
        treatment_outcome_corr = np.corrcoef(self.T_test, self.Y_test)[0, 1]
        return {
            'treatment_outcome_correlation': float(treatment_outcome_corr),
            'confounding_risk': 'high' if abs(treatment_outcome_corr) > 0.5 else 'moderate' if abs(
                treatment_outcome_corr) > 0.2 else 'low'
        }

    def _run_stability_analysis(self) -> Dict[str, Any]:
        """Run stability analysis."""
        if len(self.X_test) < 50:
            return {'status': 'insufficient_data'}

        n_bootstrap = 5
        bootstrap_ates = []

        for _ in range(n_bootstrap):
            try:
                indices = np.random.choice(len(self.X_test), size=int(0.8 * len(self.X_test)), replace=True)
                X_boot = self.X_test.iloc[indices]
                effects_boot = self.model.effect(X_boot)
                bootstrap_ates.append(np.mean(effects_boot))
            except Exception:
                continue

        if len(bootstrap_ates) >= 3:
            return {
                'bootstrap_ate_mean': float(np.mean(bootstrap_ates)),
                'bootstrap_ate_std': float(np.std(bootstrap_ates)),
                'n_successful_bootstraps': len(bootstrap_ates)
            }

        return {'status': 'insufficient_successful_bootstraps'}

    def _run_sensitivity_analysis(self) -> Dict[str, Any]:
        """Run sensitivity analysis."""
        return {'status': 'not_implemented'}

    def _run_placebo_tests(self) -> Dict[str, Any]:
        """Run placebo tests."""
        return {'status': 'not_implemented'}

    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a comprehensive evaluation report."""
        report = []
        report.append("=" * 60)
        report.append("CAUSAL MODEL EVALUATION REPORT")
        report.append("=" * 60)

        # Basic metrics
        if 'basic_metrics' in results and 'error' not in results['basic_metrics']:
            metrics = results['basic_metrics']
            report.append(f"\nBASIC METRICS:")
            report.append(f"Average Treatment Effect: {metrics.get('average_treatment_effect', 'N/A'):.4f}")
            report.append(f"ATE Standard Deviation: {metrics.get('ate_std', 'N/A'):.4f}")
            report.append(f"Sample Size: {metrics.get('n_observations', 'N/A')}")
            report.append(f"Treatment Prevalence: {metrics.get('treatment_prevalence', 'N/A'):.2%}")

        # Treatment effects
        if 'treatment_effects' in results and 'error' not in results['treatment_effects']:
            effects = results['treatment_effects']
            report.append(f"\nTREATMENT EFFECTS ANALYSIS:")
            if 'effect_distribution' in effects:
                dist = effects['effect_distribution']
                report.append(f"Effect Mean: {dist.get('mean', 'N/A'):.4f}")
                report.append(f"Effect Median: {dist.get('median', 'N/A'):.4f}")
                report.append(f"Effect Std: {dist.get('std', 'N/A'):.4f}")

        # Heterogeneity
        if 'heterogeneity' in results and 'error' not in results['heterogeneity']:
            hetero = results['heterogeneity']
            report.append(f"\nHETEROGENEITY ANALYSIS:")
            report.append(f"Effect Variance: {hetero.get('effect_variance', 'N/A'):.4f}")
            report.append(f"Effect Range: {hetero.get('effect_range', 'N/A'):.4f}")
            report.append(f"Assessment: {hetero.get('heterogeneity_assessment', 'N/A')}")

        report.append("\n" + "=" * 60)
        return "\n".join(report)


def main():
    """Main function to run causal model evaluation."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        # Get model path
        model_path = config.data_paths.get_model_file("causal_model.pkl")

        if not model_path.exists():
            logger.error(f"Model not found at {model_path}")
            return

        # Initialize evaluator
        evaluator = CausalModelEvaluator(str(model_path))

        # Run comprehensive evaluation
        logger.info("Starting comprehensive causal model evaluation...")
        results = evaluator.evaluate_comprehensive()

        # Generate and print report
        report = evaluator.generate_report(results)
        print(report)

        # Save results
        import json
        results_path = config.data_paths.get_report_file("causal_evaluation_results.json")
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"Evaluation completed. Results saved to {results_path}")

    except Exception as e:
        logger.error(f"Evaluation failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()