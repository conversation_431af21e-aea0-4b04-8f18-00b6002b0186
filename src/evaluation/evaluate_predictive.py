import os
import sys
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, Any, Tu<PERSON>, Optional, List
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.utils import load_model, load_metadata, save_model
from data.load_data import load_app_feature_data
from globals.basic_settings import config
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, roc_auc_score, classification_report,
    roc_curve, precision_recall_curve, average_precision_score
)
from sklearn.model_selection import train_test_split
from sklearn.impute import SimpleImputer


class PredictiveModelEvaluator:
    """
    A comprehensive evaluator for predictive models.

    Provides methods to evaluate predictive models including:
    - Performance metrics (accuracy, precision, recall, F1, ROC-AUC)
    - Model diagnostics and validation
    - Feature importance analysis
    - Visualization of results
    """

    def __init__(self, model_path: str, data: Optional[pd.DataFrame] = None):
        """
        Initialize the predictive model evaluator.

        Args:
            model_path: Path to the trained predictive model
            data: Optional evaluation data. If None, loads default data.
        """
        self.model_path = Path(model_path)
        self.logger = self._setup_logging()

        # Load model and metadata
        self.model = self._load_model()
        self.metadata = self._load_metadata()

        # Load or use provided data
        self.data = data if data is not None else load_app_feature_data()

        # Prepare evaluation data
        self.X_test, self.y_test = self._prepare_evaluation_data()

        self.logger.info(f"Initialized evaluator with {len(self.X_test)} test samples")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def _load_model(self):
        """Load the predictive model."""
        try:
            model = load_model(str(self.model_path))
            self.logger.info(f"Loaded predictive model from {self.model_path}")
            return model
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    def _load_metadata(self) -> Dict[str, Any]:
        """Load model metadata."""
        try:
            metadata = load_metadata(str(self.model_path))
            self.logger.info("Loaded model metadata")
            return metadata
        except Exception as e:
            self.logger.warning(f"Could not load metadata: {e}")
            return {}

    def _prepare_evaluation_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Prepare data for evaluation with proper feature alignment.

        Returns:
            Tuple of (features, target)
        """
        try:
            # Get target column
            target_col = self.metadata.get('target_column', 'no_show')

            # Extract target
            y = self.data[target_col].copy()

            # Prepare features with dimension matching
            X = self._align_features_with_model()

            # Split data for evaluation (use same random state as training)
            random_state = self.metadata.get('random_state', 42)
            test_size = self.metadata.get('test_size', 0.2)

            _, X_test, _, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state, stratify=y
            )

            self.logger.info(f"Prepared test data: {X_test.shape[0]} samples, {X_test.shape[1]} features")
            return X_test, y_test

        except Exception as e:
            self.logger.error(f"Error preparing evaluation data: {e}")
            raise

    def _align_features_with_model(self) -> pd.DataFrame:
        """
        Align features with the trained model's expected input.

        Returns:
            DataFrame with properly aligned features
        """
        # Get target column
        target_col = self.metadata.get('target_column', 'no_show')

        # Use feature columns from metadata if available
        feature_cols = self.metadata.get('feature_names')
        if feature_cols:
            self.logger.info(f"Using {len(feature_cols)} features from metadata")

            # Check which features are available in current data
            available_features = [col for col in feature_cols if col in self.data.columns]
            missing_features = [col for col in feature_cols if col not in self.data.columns]

            if missing_features:
                self.logger.warning(
                    f"Missing {len(missing_features)} features from training: {missing_features[:5]}...")

            if not available_features:
                self.logger.error("No training features found in evaluation data")
                # Fallback to all features except target
                feature_cols = [col for col in self.data.columns if col != target_col]
                X = self.data[feature_cols].copy()
            else:
                # Use available features and create dummy columns for missing ones
                X = self.data[available_features].copy()

                # Add missing features as zero columns
                for missing_col in missing_features:
                    X[missing_col] = 0

                # Reorder columns to match training order
                X = X[feature_cols]
        else:
            # Fallback: use all features except target
            self.logger.warning("No feature metadata found, using all available features")
            feature_cols = [col for col in self.data.columns if col != target_col]
            X = self.data[feature_cols].copy()

        # Handle missing values
        imputer = SimpleImputer(strategy='mean')
        X_imputed = pd.DataFrame(
            imputer.fit_transform(X),
            columns=X.columns,
            index=X.index
        )

        # Test compatibility with model
        if self._test_model_compatibility(X_imputed):
            self.logger.info(f"Successfully aligned {X_imputed.shape[1]} features with model")
            return X_imputed
        else:
            # If still not compatible, try progressive feature reduction
            return self._find_compatible_features(X_imputed)

    def _find_compatible_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Find feature subset compatible with the trained model.

        Args:
            X: Full feature DataFrame

        Returns:
            Compatible feature DataFrame
        """
        self.logger.info(f"Attempting to find compatible features from {X.shape[1]} available features")

        # Try original features first
        if self._test_model_compatibility(X):
            self.logger.info(f"Using all {X.shape[1]} features")
            return X

        # Get the expected number of features from the model if possible
        expected_features = None
        if hasattr(self.model, 'n_features_in_'):
            expected_features = self.model.n_features_in_
            self.logger.info(f"Model expects {expected_features} features")

            # Try exact match first
            if expected_features <= X.shape[1]:
                X_subset = X.iloc[:, :expected_features]
                if self._test_model_compatibility(X_subset):
                    self.logger.info(f"Using first {expected_features} features for exact match")
                    return X_subset

        # Try different feature counts around the expected number
        if expected_features:
            feature_counts = [expected_features - 5, expected_features, expected_features + 5]
        else:
            feature_counts = [20, 25, 30, 35, 40, 45, 50]

        # Add some common feature counts
        feature_counts.extend([10, 15, 60, 70, 80])
        feature_counts = sorted(set([f for f in feature_counts if 1 <= f <= X.shape[1]]))

        for n_features in feature_counts:
            X_subset = X.iloc[:, :n_features]
            if self._test_model_compatibility(X_subset):
                self.logger.info(f"Using {n_features} features for compatibility")
                return X_subset

        # Last resort: try with different feature selection strategies
        self.logger.warning("Standard feature alignment failed, trying alternative strategies")

        # Try removing categorical features that might cause issues
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            X_numeric = X[numeric_cols]
            if self._test_model_compatibility(X_numeric):
                self.logger.info(f"Using {len(numeric_cols)} numeric features only")
                return X_numeric

        # Try with just the first few features
        for n in [5, 10, 15]:
            if n <= X.shape[1]:
                X_minimal = X.iloc[:, :n]
                if self._test_model_compatibility(X_minimal):
                    self.logger.info(f"Using minimal {n} features")
                    return X_minimal

        # If all else fails, provide detailed error information
        self.logger.error(f"Feature compatibility check details:")
        self.logger.error(f"- Data shape: {X.shape}")
        self.logger.error(f"- Model type: {type(self.model).__name__}")
        self.logger.error(f"- Expected features: {expected_features}")
        self.logger.error(f"- Available features: {list(X.columns[:10])}...")

        raise ValueError(f"Could not find compatible feature set for model. "
                         f"Data has {X.shape[1]} features, model expects {expected_features or 'unknown'}")

    def _test_model_compatibility(self, X: pd.DataFrame) -> bool:
        """
        Test if features are compatible with the model.

        Args:
            X: Feature DataFrame to test

        Returns:
            True if compatible, False otherwise
        """
        try:
            # Test with a small sample
            test_sample = X.iloc[:min(5, len(X))]

            # Try prediction
            _ = self.model.predict(test_sample)

            # Also try predict_proba if available
            if hasattr(self.model, 'predict_proba'):
                _ = self.model.predict_proba(test_sample)

            return True
        except Exception as e:
            self.logger.debug(f"Compatibility test failed with {X.shape[1]} features: {str(e)}")
            return False

    def evaluate_comprehensive(self) -> Dict[str, Any]:
        """
        Run a comprehensive evaluation of the predictive model.

        Returns:
            Dictionary containing all evaluation results
        """
        results = {}

        # Make predictions
        predictions = self._safe_evaluate(
            self._make_predictions, "making predictions"
        )

        if 'error' not in predictions:
            # Basic performance metrics
            results['performance_metrics'] = self._safe_evaluate(
                lambda: self._calculate_performance_metrics(predictions),
                "calculating performance metrics"
            )

            # Classification report
            results['classification_report'] = self._safe_evaluate(
                lambda: self._generate_classification_report(predictions),
                "generating classification report"
            )

            # Confusion matrix analysis
            results['confusion_matrix'] = self._safe_evaluate(
                lambda: self._analyze_confusion_matrix(predictions),
                "analyzing confusion matrix"
            )

            # ROC and PR curve analysis
            results['curve_analysis'] = self._safe_evaluate(
                lambda: self._analyze_curves(predictions),
                "analyzing ROC and PR curves"
            )

            # Feature importance analysis
            results['feature_importance'] = self._safe_evaluate(
                self._analyze_feature_importance,
                "analyzing feature importance"
            )

            # Model diagnostics
            results['diagnostics'] = self._safe_evaluate(
                lambda: self._run_model_diagnostics(predictions),
                "running model diagnostics"
            )
        else:
            results['predictions'] = predictions

        return results

    def _safe_evaluate(self, func, description: str) -> Dict[str, Any]:
        """
        Safely execute evaluation function with error handling.

        Args:
            func: Function to execute
            description: Description for logging

        Returns:
            Function result or error information
        """
        try:
            return func()
        except Exception as e:
            self.logger.error(f"Error {description}: {e}")
            return {'error': str(e), 'status': 'failed'}

    def _make_predictions(self) -> Dict[str, Any]:
        """Make predictions on test data."""
        # Get predictions
        y_pred = self.model.predict(self.X_test)

        # Get probabilities if available
        y_proba = None
        if hasattr(self.model, 'predict_proba'):
            y_proba = self.model.predict_proba(self.X_test)
            if y_proba.shape[1] > 1:
                y_proba = y_proba[:, 1]  # Positive class probabilities

        return {
            'y_true': self.y_test,
            'y_pred': y_pred,
            'y_proba': y_proba
        }

    def _calculate_performance_metrics(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        y_true = predictions['y_true']
        y_pred = predictions['y_pred']
        y_proba = predictions['y_proba']

        metrics = {
            'accuracy': float(accuracy_score(y_true, y_pred)),
            'precision': float(precision_score(y_true, y_pred, average='weighted', zero_division=0)),
            'recall': float(recall_score(y_true, y_pred, average='weighted', zero_division=0)),
            'f1_score': float(f1_score(y_true, y_pred, average='weighted', zero_division=0)),
            'support': int(len(y_true))
        }

        # Add AUC metrics if probabilities are available
        if y_proba is not None:
            try:
                metrics['roc_auc'] = float(roc_auc_score(y_true, y_proba))
                metrics['average_precision'] = float(average_precision_score(y_true, y_proba))
            except ValueError as e:
                self.logger.warning(f"Could not calculate AUC metrics: {e}")

        return metrics

    def _generate_classification_report(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed classification report."""
        y_true = predictions['y_true']
        y_pred = predictions['y_pred']

        # Get classification report as dictionary
        report_dict = classification_report(y_true, y_pred, output_dict=True, zero_division=0)

        # Convert numpy types to Python types for JSON serialization
        def convert_types(obj):
            if isinstance(obj, dict):
                return {k: convert_types(v) for k, v in obj.items()}
            elif isinstance(obj, (np.integer, np.floating)):
                return float(obj)
            else:
                return obj

        return convert_types(report_dict)

    def _analyze_confusion_matrix(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze confusion matrix."""
        y_true = predictions['y_true']
        y_pred = predictions['y_pred']

        cm = confusion_matrix(y_true, y_pred)

        # Calculate additional metrics from confusion matrix
        if cm.shape == (2, 2):
            tn, fp, fn, tp = cm.ravel()

            analysis = {
                'confusion_matrix': cm.tolist(),
                'true_negatives': int(tn),
                'false_positives': int(fp),
                'false_negatives': int(fn),
                'true_positives': int(tp),
                'specificity': float(tn / (tn + fp)) if (tn + fp) > 0 else 0.0,
                'sensitivity': float(tp / (tp + fn)) if (tp + fn) > 0 else 0.0,
                'positive_predictive_value': float(tp / (tp + fp)) if (tp + fp) > 0 else 0.0,
                'negative_predictive_value': float(tn / (tn + fn)) if (tn + fn) > 0 else 0.0
            }
        else:
            analysis = {
                'confusion_matrix': cm.tolist(),
                'matrix_shape': cm.shape
            }

        return analysis

    def _analyze_curves(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze ROC and Precision-Recall curves."""
        y_true = predictions['y_true']
        y_proba = predictions['y_proba']

        if y_proba is None:
            return {'error': 'Probabilities not available for curve analysis'}

        try:
            # ROC Curve
            fpr, tpr, roc_thresholds = roc_curve(y_true, y_proba)
            roc_auc = roc_auc_score(y_true, y_proba)

            # Precision-Recall Curve
            precision, recall, pr_thresholds = precision_recall_curve(y_true, y_proba)
            avg_precision = average_precision_score(y_true, y_proba)

            return {
                'roc_curve': {
                    'fpr': fpr.tolist(),
                    'tpr': tpr.tolist(),
                    'thresholds': roc_thresholds.tolist(),
                    'auc': float(roc_auc)
                },
                'precision_recall_curve': {
                    'precision': precision.tolist(),
                    'recall': recall.tolist(),
                    'thresholds': pr_thresholds.tolist(),
                    'average_precision': float(avg_precision)
                }
            }
        except Exception as e:
            return {'error': f'Could not analyze curves: {str(e)}'}

    def _analyze_feature_importance(self) -> Dict[str, Any]:
        """Analyze feature importance if available."""
        try:
            if hasattr(self.model, 'feature_importances_'):
                importances = self.model.feature_importances_
                feature_names = self.X_test.columns.tolist()

                # Create feature importance dataframe
                importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': importances
                }).sort_values('importance', ascending=False)

                return {
                    'available': True,
                    'top_10_features': importance_df.head(10).to_dict('records'),
                    'total_features': len(feature_names),
                    'importance_sum': float(np.sum(importances))
                }
            else:
                return {
                    'available': False,
                    'reason': 'Model does not support feature importance'
                }
        except Exception as e:
            return {'error': f'Could not analyze feature importance: {str(e)}'}

    def _run_model_diagnostics(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Run model diagnostics."""
        y_true = predictions['y_true']
        y_pred = predictions['y_pred']
        y_proba = predictions['y_proba']

        diagnostics = {
            'class_distribution': {
                'actual': dict(y_true.value_counts().to_dict()),
                'predicted': dict(pd.Series(y_pred).value_counts().to_dict())
            },
            'prediction_confidence': self._analyze_prediction_confidence(y_proba),
            'model_metadata': {
                'model_type': str(type(self.model).__name__),
                'n_features': self.X_test.shape[1],
                'n_samples': len(y_true)
            }
        }

        return diagnostics

    def _analyze_prediction_confidence(self, y_proba: Optional[np.ndarray]) -> Dict[str, Any]:
        """Analyze prediction confidence distribution."""
        if y_proba is None:
            return {'available': False, 'reason': 'Probabilities not available'}

        try:
            confidence_stats = {
                'mean_probability': float(np.mean(y_proba)),
                'std_probability': float(np.std(y_proba)),
                'min_probability': float(np.min(y_proba)),
                'max_probability': float(np.max(y_proba)),
                'high_confidence_ratio': float(np.mean((y_proba > 0.8) | (y_proba < 0.2))),
                'uncertain_predictions_ratio': float(np.mean((y_proba > 0.4) & (y_proba < 0.6)))
            }

            return confidence_stats
        except Exception as e:
            return {'error': f'Could not analyze confidence: {str(e)}'}

    def visualize_results(self, results: Dict[str, Any], save_path: Optional[str] = None):
        """Create visualizations of evaluation results."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('Predictive Model Evaluation Results', fontsize=16)

            # 1. Confusion Matrix
            if 'confusion_matrix' in results and 'confusion_matrix' in results['confusion_matrix']:
                cm = np.array(results['confusion_matrix']['confusion_matrix'])
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
                axes[0, 0].set_title('Confusion Matrix')
                axes[0, 0].set_xlabel('Predicted')
                axes[0, 0].set_ylabel('Actual')

            # 2. Feature Importance (if available)
            if ('feature_importance' in results and
                    results['feature_importance'].get('available', False)):
                top_features = results['feature_importance']['top_10_features']
                features = [f['feature'] for f in top_features]
                importances = [f['importance'] for f in top_features]

                axes[0, 1].barh(features[::-1], importances[::-1])
                axes[0, 1].set_title('Top 10 Feature Importances')
                axes[0, 1].set_xlabel('Importance')

            # 3. ROC Curve (if available)
            if ('curve_analysis' in results and
                    'roc_curve' in results['curve_analysis']):
                roc_data = results['curve_analysis']['roc_curve']
                axes[1, 0].plot(roc_data['fpr'], roc_data['tpr'],
                                label=f"ROC (AUC = {roc_data['auc']:.3f})")
                axes[1, 0].plot([0, 1], [0, 1], 'k--', label='Random')
                axes[1, 0].set_xlabel('False Positive Rate')
                axes[1, 0].set_ylabel('True Positive Rate')
                axes[1, 0].set_title('ROC Curve')
                axes[1, 0].legend()

            # 4. Performance Metrics Bar Chart
            if 'performance_metrics' in results:
                metrics = results['performance_metrics']
                metric_names = ['accuracy', 'precision', 'recall', 'f1_score']
                metric_values = [metrics.get(name, 0) for name in metric_names]

                bars = axes[1, 1].bar(metric_names, metric_values)
                axes[1, 1].set_title('Performance Metrics')
                axes[1, 1].set_ylabel('Score')
                axes[1, 1].set_ylim(0, 1)

                # Add value labels on bars
                for bar, value in zip(bars, metric_values):
                    axes[1, 1].text(bar.get_x() + bar.get_width() / 2, bar.get_height() + 0.01,
                                    f'{value:.3f}', ha='center', va='bottom')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Visualization saved to {save_path}")

            plt.show()

        except Exception as e:
            self.logger.error(f"Error creating visualizations: {e}")

    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a comprehensive evaluation report."""
        report = []
        report.append("=" * 60)
        report.append("PREDICTIVE MODEL EVALUATION REPORT")
        report.append("=" * 60)

        # Model Information
        if 'diagnostics' in results and 'model_metadata' in results['diagnostics']:
            metadata = results['diagnostics']['model_metadata']
            report.append(f"\nMODEL INFORMATION:")
            report.append(f"Model Type: {metadata.get('model_type', 'N/A')}")
            report.append(f"Number of Features: {metadata.get('n_features', 'N/A')}")
            report.append(f"Test Samples: {metadata.get('n_samples', 'N/A')}")

        # Performance Metrics
        if 'performance_metrics' in results and 'error' not in results['performance_metrics']:
            metrics = results['performance_metrics']
            report.append(f"\nPERFORMANCE METRICS:")
            report.append(f"Accuracy: {metrics.get('accuracy', 'N/A'):.4f}")
            report.append(f"Precision: {metrics.get('precision', 'N/A'):.4f}")
            report.append(f"Recall: {metrics.get('recall', 'N/A'):.4f}")
            report.append(f"F1 Score: {metrics.get('f1_score', 'N/A'):.4f}")
            if 'roc_auc' in metrics:
                report.append(f"ROC AUC: {metrics.get('roc_auc', 'N/A'):.4f}")

        # Confusion Matrix Analysis
        if 'confusion_matrix' in results and 'error' not in results['confusion_matrix']:
            cm_analysis = results['confusion_matrix']
            if 'true_positives' in cm_analysis:
                report.append(f"\nCONFUSION MATRIX ANALYSIS:")
                report.append(f"True Positives: {cm_analysis.get('true_positives', 'N/A')}")
                report.append(f"True Negatives: {cm_analysis.get('true_negatives', 'N/A')}")
                report.append(f"False Positives: {cm_analysis.get('false_positives', 'N/A')}")
                report.append(f"False Negatives: {cm_analysis.get('false_negatives', 'N/A')}")
                report.append(f"Specificity: {cm_analysis.get('specificity', 'N/A'):.4f}")
                report.append(f"Sensitivity: {cm_analysis.get('sensitivity', 'N/A'):.4f}")

        # Feature Importance
        if ('feature_importance' in results and
                results['feature_importance'].get('available', False)):

            top_features = results['feature_importance']['top_10_features']
            report.append(f"\nTOP 10 IMPORTANT FEATURES:")
            for i, feature in enumerate(top_features, 1):
                report.append(f"{i:2d}. {feature['feature']}: {feature['importance']:.4f}")

        report.append("\n" + "=" * 60)
        return "\n".join(report)


def main():
    """Main function to run predictive model evaluation."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        # Get model path
        model_path = config.data_paths.get_model_file("predictive_model.pkl")

        if not model_path.exists():
            logger.error(f"Model not found at {model_path}")
            return

        # Initialize evaluator
        evaluator = PredictiveModelEvaluator(str(model_path))

        # Run comprehensive evaluation
        logger.info("Starting comprehensive predictive model evaluation...")
        results = evaluator.evaluate_comprehensive()

        # Generate and print report
        report = evaluator.generate_report(results)
        print(report)

        # Create visualizations
        viz_path = config.data_paths.get_report_file("predictive_evaluation_plots.png")
        evaluator.visualize_results(results, str(viz_path))

        # Save results
        results_path = config.data_paths.get_model_file("predictive_evaluation_results.json")
        with open(results_path, 'w') as f:
            save_model(results, results_path)

        logger.info(f"Evaluation completed. Results saved to {results_path}")

    except Exception as e:
        logger.error(f"Evaluation failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()