"""
Main entry point for causal model training.
Refactored to use the interface pattern.
"""
import os,sys
import logging
import pandas as pd

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from data import load_data as ld
from models.causal_trainer import CausalModelTrainer

def main():
    """Main function for training causal models."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        data = ld.load_app_feature_data()
        _extracted_from_main(logger, data)
    except Exception as e:
        logger.error(f"An error occurred: {e}", exc_info=True)

def _extracted_from_main(logger, data:pd.DataFrame):
    """Extracted main logic for training causal models."""
    trainer = CausalModelTrainer(data=data,treatment_column='sms_sent',outcome_column='no_show',model_name='causal_model')

    models_to_train = [
        ('causal_forest', 'random_forest', 'random_forest'),
        ('linear_dml', 'ridge', 'ridge'),
        ('linear_dml', 'random_forest', 'ridge'),
    ]

    results = []

    for model_type, outcome_model, treatment_model in models_to_train:
        logger.info(f"{'=' * 60}")
        logger.info(f"Training {model_type} with outcome_model={outcome_model}, treatment_model={treatment_model}")
        logger.info(f"{'=' * 60}")

        try:
            trainer.train(
                model_type=model_type,
                outcome_model=outcome_model,
                treatment_model=treatment_model
            )
            results.append(trainer.get_model_summary())
        except Exception as e:
            logger.error(f"Failed to train {model_type}: {e}")
            results.append({
                'model_type': model_type,
                'error': str(e),
                'ate': None,
                'ate_std': None,
                'reliability': {'is_reliable': False, 'warnings': [f"Training failed: {e}"]}
            })

    # Summary of all results
    logger.info(f"{'=' * 60}")
    logger.info("FINAL RESULTS SUMMARY")
    logger.info(f"{'=' * 60}")

    for i, result in enumerate(results):
        logger.info(f"\nModel {i + 1}: {result['model_type']}")
        if result.get('error'):
            logger.error(f"  Error: {result['error']}")
        else:
            logger.info(f"  ATE: {result['ate']}")
            logger.info(f"  ATE Std: {result['ate_std']}")
            if result.get('reliability'):
                reliability = result['reliability']
                logger.info(f"  Reliable: {reliability.get('is_reliable', 'Unknown')}")
                if reliability.get('warnings'):
                    logger.warning(f"  Warnings: {len(reliability['warnings'])} issues detected")

    return results

if __name__ == "__main__":
    main()