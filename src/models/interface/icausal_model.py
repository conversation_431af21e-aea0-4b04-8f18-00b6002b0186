import pandas as pd

from abc import ABC, abstractmethod
from typing import Dict, <PERSON><PERSON>, Tuple, Any

class ICausalModel(ABC):
    """Base interface for all causal interface models."""

    def __init__(self,
                 data: pd.DataFrame,
                 treatment_column: str,
                 outcome_column: str,
                 test_size: float = 0.2,
                 random_state: int = 42,
                 model_name: str = "causal_model"):
        self.data = data.copy(deep=True)
        self.treatment_column = treatment_column
        self.outcome_column = outcome_column
        self.test_size = test_size
        self.random_state = random_state
        self.model_name = model_name

    @abstractmethod
    def preprocess_data(self) -> <PERSON>ple[pd.DataFrame, pd.Series, pd.Series]:
        """Preprocess data for causal inference."""
        pass

    @abstractmethod
    def split_data(self) -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series, pd.Series, pd.Series]:
        """Split data into training and testing sets."""
        pass

    @abstractmethod
    def train(self, **kwargs) -> Any:
        """Train the causal model."""
        pass

    @abstractmethod
    def evaluate(self) -> Dict[str, Any]:
        """Evaluate the causal model."""
        pass

    @abstractmethod
    def get_model_summary(self) -> Dict[str, Any]:
        """Get a summary of the causal model."""
        pass

    @abstractmethod
    def _validate_inputs(self):
        """validate input parameters and data."""
        pass