from abc import ABC, abstractmethod
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, Any, List
import pandas as pd
import numpy as np

class IPredictiveModel(ABC):
    """Base interface for all predictive models."""

    def __init__(self,
                 data: pd.DataFrame,
                 target_column: str,
                 test_size: float = 0.2,
                 random_state: int = 42,
                 model_name: str = "predictive_model"):
        self.data = data.copy(deep=True)
        self.target_column = target_column
        self.test_size = test_size
        self.random_state = random_state
        self.model_name = model_name
        self.model_type = None
        self.model = None
        self.best_params = None
        self.cv_score = None
        self.test_metrics = None
        self.feature_names = None
        self.feature_importance = None

    @abstractmethod
    def preprocess_data(self) -> <PERSON><PERSON>[pd.DataFrame, pd.Series]:
        """Preprocess data for predictive modeling."""
        pass

    @abstractmethod
    def split_data(self) -> <PERSON>ple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """Split data into training and testing sets."""
        pass

    @abstractmethod
    def train(self, model_type: str = 'random_forest', **kwargs) -> Any:
        """Train the predictive model."""
        pass

    @abstractmethod
    def predict(self, new_data: pd.DataFrame) -> np.ndarray:
        """Make predictions on new data."""
        pass

    @abstractmethod
    def predict_proba(self, new_data: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities for new data."""
        pass

    @abstractmethod
    def get_model_summary(self) -> Dict[str, Any]:
        """Get a summary of the predictive model."""
        pass

    @abstractmethod
    def _validate_inputs(self, data: pd.DataFrame, target_column: str, test_size: float):
        """Validate input parameters and data."""
        pass

    @abstractmethod
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        pass

    @abstractmethod
    def _preprocess_new_data(self, new_data: pd.DataFrame) -> pd.DataFrame:
        """Apply same preprocessing pipeline to new data."""
        pass