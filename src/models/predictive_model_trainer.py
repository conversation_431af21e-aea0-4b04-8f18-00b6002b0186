import os
import sys
# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import warnings
import logging
import datetime
import pandas as pd
import numpy as np

from interface.ipredictive_model import IPredictiveModel
from common.utils import save_model, load_model, get_model_save_path
from globals.basic_settings import config
from sklearn.ensemble import GradientBoostingClassifier
from xgboost import XGBClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    roc_auc_score,
    accuracy_score,
    precision_score,
    recall_score,
    f1_score
)
from sklearn.impute import SimpleImputer
from nltk.classify.svm import SvmClassifier
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path


class PredictiveModelTrainer(IPredictiveModel):
    """
    A comprehensive class for training predictive models with proper data preprocessing,
    validation, and model management.

    Features:
    - Multiple model types support
    - Automated hyperparameter tuning
    - Comprehensive evaluation metrics
    - Model persistence with metadata
    - Robust error handling and logging
    """

    # Supported model types and their configuration
    _SUPPORTED_MODELS = {
        'logistic_regression': {
            'class': LogisticRegression,
            'default_params': {
                'C': [0.01, 0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'max_iter': [10000],
                'solver': ['liblinear', 'saga']
            }
        },
        'logistic_regression_lbfgs_newton': {
            'class': LogisticRegression,
            'default_params': {
                'C': [0.01, 0.1, 1, 10, 100],
                'penalty': ['l2'],
                'max_iter': [10000],
                'solver': ['lbfgs', 'newton-cholesky']
            }
        },
        'random_forest': {
            'class': RandomForestClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2']
            }
        },
        'gradient_boosting': {
            'class': GradientBoostingClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.1, 0.2, 0.5],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
        },
        'svm': {
            'class': SvmClassifier,
            'default_params': {
                'C': [0.01, 0.1, 1, 10],
                'kernel': ['linear', 'rbf', 'poly'],
                'degree': [2, 3, 4],
                'gamma': ['scale', 'auto']
            }
        },
        'svc': {
            'class': SVC,
            'default_params': {
                'C': [0.01, 0.1, 1, 10],
                'kernel': ['rbf', 'linear'],
                'gamma': ['scale', 'auto']
            }
        },
        'xgboost': {
            'class': XGBClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.1, 0.2, 0.5],
                'max_depth': [None, 10, 20, 30],
                'min_child_weight': [1, 5, 10],
                'subsample': [0.8, 0.9, 1.0]
            }
        }
    }

    def __init__(self, data: pd.DataFrame, target_column: str,
                 test_size: float = 0.2, random_state: int = 42,
                 model_name: str = "predictive_model"):
        """Initialize the predictive model trainer."""
        # Input validation
        self._validate_inputs(data, target_column, test_size)

        # Initialize parent
        super().__init__(data, target_column, test_size, random_state, model_name)

        # Model and preprocessing components
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _validate_inputs(self, data: pd.DataFrame, target_column: str, test_size: float):
        """Validate input parameters and data."""
        if not isinstance(data, pd.DataFrame):
            raise ValueError("Data must be a pandas DataFrame")

        if data.empty:
            raise ValueError("Data cannot be empty")

        if target_column not in data.columns:
            raise ValueError(
                f"Target column '{target_column}' not found in data")

        if test_size <= 0 or test_size >= 1:
            raise ValueError("test_size must be between 0 and 1")

        # Check if target has least 2 classes
        if len(data[target_column].unique()) < 2:
            raise ValueError("Target must have at least 2 classes")

    def preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """Preprocess data for predictive modeling."""
        # Define columns to exclude
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or col in ['patient_id', 'appointment_id', self.target_column]
        ]

        # Separate features and target
        X = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
        y = self.data[self.target_column]

        self.logger.info(f"Original features: {X.shape[1]}")
        self.logger.info(f"Excluded columns: {len(exclude_columns)}")

        # Handle categorical features
        categorical_columns = X.select_dtypes(include=['object', 'category']).columns
        
        if len(categorical_columns) > 0:
            self.logger.info(f"Encoding {len(categorical_columns)} categorical columns")
            X_encoded = pd.get_dummies(X, columns=categorical_columns, drop_first=True)
        else:
            X_encoded = X.copy()

        # Store feature names for later use
        self.feature_names = list(X_encoded.columns)
        self.logger.info(f"Final feature count: {len(self.feature_names)}")

        return X_encoded, y

    def split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """Split data with stratification and proper preprocessing pipeline."""
        X, y = self.preprocess_data()

        # Check class distribution before splitting
        class_counts = y.value_counts()
        self.logger.info(f"Class distribution before split: {class_counts}")

        # Ensure minimum sampler per class for stratification
        min_class_size = class_counts.min()
        if min_class_size < 2:
            self.logger.warning(f"Minimum class size is {min_class_size}. Need at least 2 samples per class for stratification.")
            stratify = None
        else:
            stratify = y

        # Stratified split to maintain class distribution
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=self.test_size, random_state=self.random_state, stratify=stratify)

        # Fit imputer on training data only (prevent data leakage)
        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )

        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test),
            columns=X_test.columns,
            index=X_test.index
        )

        # Log data quality information
        self.logger.info(f"Training set: {X_train_imputed.shape}, Test set: {X_test_imputed.shape}")
        self.logger.info(f"Missing values in training set: {X_train_imputed.isnull().sum().sum()}")
        self.logger.info(f"Missing values in test set: {X_test_imputed.isnull().sum().sum()}")

        return X_train_imputed, X_test_imputed, y_train, y_test

    def train(self,
              model_type: str = 'random_forest',
              custom_params: Optional[Dict] = None,
              scoring: str = 'roc_auc',
              cv_folds: int = 5,
              refit: bool = True) -> Any:
        """Train model with comprehensive hyperparameter tuning and validation."""

        # Validate model type
        if model_type not in self._SUPPORTED_MODELS:
            available_models = list(self._SUPPORTED_MODELS.keys())
            raise ValueError(f"Model type '{model_type}' is not supported. Available models: {available_models}")

        self.logger.info(f"Training {model_type} model...")

        # Split data
        X_train, X_test, y_train, y_test = self.split_data()

        # Check class distribution
        class_distribution = y_train.value_counts(normalize=True)
        self.logger.info(f"Class distribution in training set: {class_distribution.to_dict()}")

        # Check for class imbalance
        min_class_ratio = class_distribution.min()
        if min_class_ratio < 0.1:
            self.logger.warning(f"Class imbalance detected. Min class ratio: {min_class_ratio:.3f}")

        # Get model configuration
        model_config = self._SUPPORTED_MODELS[model_type]
        self.model_type = model_type
        model_class = model_config['class']

        # Initialize base model with appropriate parameters
        base_model_params = {'random_state': self.random_state}

        # Add class balancing for applicable models
        if hasattr(model_class(), 'class_weight'):
            base_model_params['class_weight'] = 'balanced'

        # Special handling for SVM probability
        if model_type == 'svm':
            base_model_params['probability'] = True

        base_model = model_class(**base_model_params)

        # Setup parameter grid
        param_grid = custom_params if custom_params is not None else model_config['default_params']

        # Configure cross-validation
        cv_strategy = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)

        # Initialize GridSearchCV
        self.logger.info(f"Starting grid search with {cv_folds} folds...")
        grid_search = GridSearchCV(estimator=base_model, 
                                   param_grid=param_grid,
                                   cv=cv_strategy, 
                                   scoring=scoring, 
                                   n_jobs=-1, 
                                   verbose=3)

        # Suppress warnings during grid search
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            grid_search.fit(X_train, y_train)

        # Store results
        self.model = grid_search.best_estimator_
        self.best_params = grid_search.best_params_
        self.cv_score = grid_search.best_score_

        self.logger.info(f"Best parameters found: {self.best_params}")
        self.logger.info(f"Best cross-validation score: {self.cv_score:.4f}")

        # Evaluate on test set
        y_pred = self.model.predict(X_test)

        # Get probabilities if available
        if hasattr(self.model, 'predict_proba'):
            y_proba = self.model.predict_proba(X_test)[:, 1]
        else:
            y_proba = None
            self.logger.warning("Model does not support probability predictions.")

        # Calculate comprehensive metrics
        self.test_metrics = self._calculate_metrics(y_test, y_pred, y_proba)
        self.logger.info(f"Test set evaluation metrics: {self.test_metrics}")

        # Additional detailed evaluation
        self._detailed_evaluation(X_test, y_test, y_pred, y_proba)

        # Save model
        self._save_model_with_metadata()

        return self.model

    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }

        # Add AUC metrics if probabilities are available
        if y_proba is not None:
            try:
                metrics['roc_auc'] = roc_auc_score(y_true, y_proba)
            except ValueError as e:
                self.logger.warning(f"Could not calculate AUC metrics: {e}")
                metrics['roc_auc'] = None

        return metrics

    def _detailed_evaluation(self, X_test: pd.DataFrame, y_test: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray]):
        """Provide detailed model evaluation including feature importance."""
        self.logger.info("Generating detailed evaluation report...")

        # Classification report
        self.logger.info("\nClassification Report:")
        self.logger.info(classification_report(y_test, y_pred, output_dict=True))

        report = classification_report(y_test, y_pred, output_dict=True)
        for class_name, metrics in report.items():
            if isinstance(metrics, dict):
                self.logger.info(f" {class_name}: {metrics}")

        # Confusion matrix
        cm = confusion_matrix(y_test, y_pred)
        self.logger.info(f"\nConfusion Matrix:\n{cm}")

        # Prediction confidence analysis
        if y_proba is not None:
            self._extracted_from__detailed_evaluation(y_proba)
            
        # Test set statistics
        self.logger.info("\nTest Set Statistics:")
        self.logger.info(f" Shape: {X_test.shape}")
        self.logger.info(f" Missing Values: {X_test.isnull().sum().sum()}")
        self.logger.info(f" Feature range: [{X_test.min().min():.4f}, {X_test.max().max():.4f}]")

        # Feature importance for applicable models
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance = pd.DataFrame({
                    'feature': self.feature_names,
                    'importance': self.model.feature_importances_
                }).value_counts('importance', ascending=False)

            self.logger.info(f"\nFeature Importance:\n{self.feature_importance}")

            for idx, row in self.feature_importance.head(10).iterrows():
                feature_name = row['feature']
                importance = row['importance']
                # Add feature statistics from test set
                if feature_name in X_test.columns:
                    feature_mean = row[feature_name].mean()
                    feature_std = X_test[feature_name].std()
                    self.logger.info(f" {feature_name}: {importance:.4f} (mean: {feature_mean:.3f}, std: {feature_std:.3f})")
                else:
                    self.logger.info(f" {feature_name}: {importance:.4f}")  
                self.logger.info(f" {idx+1}. {row['feature']}: {row['importance']:.4f}")
        else:
            self.logger.info("Model does not support feature importance.")

    def _extracted_from__detailed_evaluation(self, y_proba):
        self.logger.info("\nPrediction Confidence Analysis:")
        self.logger.info(f" Mean probability: {y_proba.mean():.4f}")
        self.logger.info(f" Std probability: {y_proba.std():.4f}")
        high_conf = np.mean((y_proba > 0.8) | (y_proba < 0.2))
        uncertain = np.mean((y_proba > 0.4) & (y_proba < 0.6))
        self.logger.info(f" High confidence ratio: {high_conf:.4f}")
        self.logger.info(f" Uncertain predictions ratio: {uncertain:.4f}")

    def _save_model_with_metadata(self):
        """Save model with comprehensive metadata."""
        try:
            model_path = get_model_save_path(
                self.model_name, self.model_type, use_user_dir=False)
            self.logger.info(f"Saving model to {model_path}...")

            # Prepare comprehensive metadata
            model_metadata = {
                'model_name': self.model_name,
                'model_type': type(self.model).__name__,
                'best_params': self.best_params,
                'cv_score': self.cv_score,
                'test_metrics': self.test_metrics,
                'feature_names': self.feature_names,
                'target_column': self.target_column,
                'n_features': len(self.feature_names) if self.feature_names else 0,
                'training_data_shape': self.data.shape,
                'random_state': self.random_state,
                'training_timestamp': datetime.datetime.now(datetime.UTC).isoformat()
            }

            # Add feature importance if available
            if self.feature_importance is not None:
                model_metadata['feature_importance'] = self.feature_importance.head(
                    10).to_dict('records')

            # Save model with metadata using utils
            save_model(self.model, model_path, model_metadata)
            self.logger.info(
                f"Predictive model and metadata saved successfully to {model_path}")

        except Exception as e:
            self.logger.error(f"Error saving model: {e}")
            raise

    def predict(self, new_data: pd.DataFrame) -> np.ndarray:
        """Make predictions on new data using the trained model."""
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")

        # Apply same preprocessing as training
        preprocessed_data = self._preprocess_new_data(new_data)
        # Make predictions
        return self.model.predict(preprocessed_data)

    def predict_proba(self, new_data: pd.DataFrame) -> np.ndarray:
        """Get prediction probabilities for new data."""
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")

        if not hasattr(self.model, 'predict_proba'):
            raise ValueError("Model does not support probability predictions.")

        # Apply same preprocessing as training
        preprocessed_data = self._preprocess_new_data(new_data)
        # Get probabilities
        return self.model.predict_proba(preprocessed_data)[:, 1]

    def _preprocess_new_data(self, new_data: pd.DataFrame) -> pd.DataFrame:
        """Apply same preprocessing as training data."""
        if self.feature_names is None:
            raise ValueError("Model has not been trained yet. Feature names not available.")

        # Apply same exclusions as training
        exclude_columns = [col for col in new_data.columns if col.startswith(
            'neighbourhood_') or col in ['patient_id', 'appointment_id']]

        # Remove excluded columns
        X = new_data.drop(columns=[col for col in exclude_columns if col in new_data.columns])

        # Handle categorical encoding (same as training)
        categorical_columns = X.select_dtypes(
            include=['object', 'category']).columns

        if len(categorical_columns) > 0:
            X_encoded = pd.get_dummies(X, columns=categorical_columns, drop_first=True)
        else:
            X_encoded = X.copy()

        # Ensure same features as training
        missing_features = set(self.feature_names) - set(X_encoded.columns)
        extra_features = set(X_encoded.columns) - set(self.feature_names)

        # Add missing features with zeros
        for feature in missing_features:
            X_encoded[feature] = 0

        # Drop extra features
        X_encoded = X_encoded.drop(columns=extra_features)

        return pd.DataFrame(self.imputer.transform(X_encoded), columns=X_encoded.columns, index=X_encoded.index)


    def get_model_summary(self) -> Dict[str, Any]:
        """Get a comprehensive summary of trained model."""
        if self.model is None:
            return {"status": "No model trained yet."}

        summary = {
            'model_name': self.model_name,
            'model_type': type(self.model).__name__,
            'n_features': len(self.feature_names) if self.feature_names else 0,
            'target_column': self.target_column,
            'best_params': self.best_params,
            'cv_score': self.cv_score,
            'test_metrics': self.test_metrics,
            'training_data_shape': self.data.shape,
            # First 10 features
            'feature_names': self.feature_names[:10] if self.feature_names else None
        }

        # Add feature importance summary if available
        if self.feature_importance is not None:
            summary['top_5_features'] = self.feature_importance.head(
                5).to_dict('records')

        return summary

    def load_model(self, model_path: str) -> 'PredictiveModelTrainer':
        """Load a previously trained model."""
        try:
            self.model = load_model(model_path)
            self.logger.info(f"Model loaded from {model_path}")
            return self
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise