from models.interface.ipredictive_model import IPredictiveModel
from common.utils import save_model, load_model, get_model_save_path
from globals.basic_settings import config
from sklearn.ensemble import GradientBoostingClassifier
from xgboost import XGBClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.metrics import (
    classification_report, confusion_matrix, roc_auc_score,
    accuracy_score, precision_score, recall_score, f1_score
)
from sklearn.impute import SimpleImputer
from nltk.classify.svm import SvmClassifier
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
import warnings
import logging
import datetime
import pandas as pd
import numpy as np
import os
import sys
# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class PredictiveModelTrainer(IPredictiveModel):
    """
    A comprehensive class for training predictive models with proper data preprocessing,
    validation, and model management.

    Features:
    - Multiple model types support
    - Automated hyperparameter tuning
    - Comprehensive evaluation metrics
    - Model persistence with metadata
    - Robust error handling and logging
    """

    # Supported model types and their configuration
    _SUPPORTED_MODELS = {
        'logistic_regression': {
            'class': LogisticRegression,
            'default_params': {
                'C': [0.01, 0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'max_iter': [1000, 2000, 5000],
                'solver': ['liblinear', 'saga']
            }
        },
        'logistic_regression_lbfgs_newton': {
            'class': LogisticRegression,
            'default_params': {
                'C': [0.01, 0.1, 1, 10, 100],
                'penalty': ['l2'],
                'max_iter': [1000, 2000, 5000],
                'solver': ['lbfgs', 'newton-cholesky']
            }
        },
        'random_forest': {
            'class': RandomForestClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2']
            }
        },
        'gradient_boosting': {
            'class': GradientBoostingClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.1, 0.2, 0.5],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
        },
        'svm': {
            'class': SvmClassifier,
            'default_params': {
                'C': [0.01, 0.1, 1, 10],
                'kernel': ['linear', 'rbf', 'poly'],
                'degree': [2, 3, 4],
                'gamma': ['scale', 'auto']
            }
        },
        'svc': {
            'class': SvcClassifier,
            'default_params': {
                'C': [0.01, 0.1, 1, 10],
                'kernel': ['rbf', 'linear'],
                'gamma': ['scale', 'auto']
            }
        },
        'xgboost': {
            'c'
        }
    }
