"""
Main entry point for predictive model training.
Refactored to use the interface pattern.
"""
import os
import sys
import logging

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.predictive_model_trainer import PredictiveModelTrainer
from data import load_data as ld


def main():
    """
    Comprehensive example usage of the refactored PredictiveModelTrainer.
    Demonstrates training multiple models and comparing their performance.
    """
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        logger.info("Starting predictive model training pipeline...")

        # Load feature engineered data
        data = ld.load_app_feature_data()
        logger.info(f"Loaded data with shape: {data.shape}")

        # Initialize predictive model trainer
        trainer = PredictiveModelTrainer(
            data=data,
            target_column='no_show',
            model_name='predictive_model'
        )

        # Models to train and compare
        models_to_train = [
            ('random_forest', 'Random Forest'),
            ('logistic_regression', 'Logistic Regression'),
            ('logistic_regression_lbfgs_newton',
             'Logistic Regression (lbfgs/newton)'),
            ('gradient_boosting', 'Gradient Boosting'),
            ('svm', 'Support Vector Machine'),
            ('svc', 'Support-Vector Classifier'),
            ('xgboost', 'XGBoost')
        ]

        results = []

        for model_type, model_name in models_to_train:
            logger.info(f"{'=' * 60}")
            logger.info(f"Training {model_name} ({model_type})")
            logger.info(f"{'=' * 60}")

            try:
                # Train the model
                model = trainer.train(model_type=model_type)

                # Get model summary
                summary = model.get_model_summary()
                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'summary': summary
                })

                logger.info(f"✓ {model_name} training completed successfully!")

            except Exception as e:
                logger.error(f"✗ Failed to train {model_name}: {e}")
                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'error': str(e)
                })

        # Summary of all results
        logger.info("\n")
        logger.info(f"{'=' * 60}")
        logger.info("TRAINING RESULTS SUMMARY")
        logger.info(f"{'=' * 60}")

        for result in results:
            model_name = result['model_name']
            if 'error' in result:
                logger.error(f"{model_name}: FAILED - {result['error']}")
            else:
                summary = result['summary']
                cv_score = summary.get('cv_score', 'N/A')
                test_metrics = summary.get('test_metrics', {})
                accuracy = test_metrics.get('accuracy', 'N/A')

                logger.info(f"  Model: {model_name}:")
                logger.info(f"  CV Score: {cv_score}")
                logger.info(f"  Test Accuracy: {accuracy}")
                logger.info(f"  Features: {summary.get('n_features', 'N/A')}")

        logger.info("\n✓ Predictive model training pipeline completed!")

    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
