"""
Main entry point for predictive model training.
Refactored to use the interface pattern.
"""
import os
import sys
import time
import logging

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tqdm import tqdm
from data import load_data as ld
from models.predictive_model_trainer import PredictiveModelTrainer
from common.progress_tracker import ModelComparisonTracker, create_progress_logger


def main():
    """
    Comprehensive example usage of the refactored PredictiveModelTrainer.
    Demonstrates training multiple models and comparing their performance.
    """
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    # Create optimized logger for progress tracking
    logger = create_progress_logger("predictive_training")
   

    try:
        logger.info("Starting predictive model training pipeline...")

        # Load feature engineered data
        data = ld.load_app_feature_data()
        logger.info(f"Loaded data with shape: {data.shape}")

        # Initialize predictive model trainer
        trainer = PredictiveModelTrainer(
            data=data,
            target_column='no_show',
            model_name='predictive_model'
        )

        # Models to train and compare
        models_to_train = [
            ('random_forest', 'Random Forest'),
            ('logistic_regression', 'Logistic Regression'),
            ('logistic_regression_lbfgs_newton',
             'Logistic Regression (lbfgs/newton)'),
            ('gradient_boosting', 'Gradient Boosting'),
            ('svm', 'Support Vector Machine'),
            ('svc', 'Support-Vector Classifier'),
            ('xgboost', 'XGBoost')
        ]

        # Initialize progress tracker
        model_names = [name for _, name in models_to_train]
        progress_tracker = ModelComparisonTracker(model_names, logger)
        progress_tracker.start_comparison()

        results = []
        total_models = len(models_to_train)
        
        # Create progress bar for overall training progress
        with tqdm(total=total_models, desc="Training Models", unit="model") as pbar:
            # training loop
            pbar.update(1)
            
            for i, (model_type, model_name) in enumerate(models_to_train, 1):
                logger.info(f"{'=' * 60}")
                logger.info(f"Training {model_name} ({model_type}) - Model {i}/{total_models}")
                logger.info(f"{'=' * 60}")
                
                # Update progress bar description
                pbar.set_description(f"Training {model_name }")
                
                start_time = time.time()
                

        for model_type, model_name in models_to_train:
            # Start tracking this model
            start_time = progress_tracker.start_model_training(model_name)

            try:
                # Train the model
                model = trainer.train(model_type=model_type)

                # Get model summary
                summary = model.get_model_summary()
                
                # Calculate training time
                training_time = time.time() - start_time
                test_metrics = summary.get('test_metrics', {})

                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'training_time': training_time,
                    'summary': summary
                })
                
                logger.info(f"✓ {model_name} completed successfully in {training_time:.2f}s!")
                # Finish tracking this model with success
                progress_tracker.finish_model_training(
                    model_name, start_time, success=True, metrics=test_metrics
                )

            except Exception as e:
                training_time = time.time() - start_time
                logger.info(f"✗ {model_name} failed to train after {training_time:.2f}s: {e}")
                logger.error(f"Error details: {e}")
                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'error': str(e),
                    'training_time': training_time
                })
                
                # Update progress bar
                pbar.update(1)
                pbar.set_postfix({
                    'Status': '✓ Success' if 'error' not in results[-1] else '✗ Failed', 
                    'Time': f"{training_time:.1f}s"
                })

                # Finish tracking this model with failure
                progress_tracker.finish_model_training(
                    model_name, start_time, success=False, error=str(e)
                )

        # Finish comparison tracking
        progress_tracker.finish_comparison()
        
        # Summary of all results
        logger.info("\n")
        logger.info(f"{'=' * 60}")
        logger.info("TRAINING RESULTS SUMMARY")
        logger.info(f"{'=' * 60}")

        # Additional detailed summary
        logger.info("DETAILED RESULTS:")
        logger.info("-" * 40)

        for result in results:
            model_name = result['model_name']
            training_time = result.get('training_time', 0)
            
            if 'error' in result:
                logger.error(f"{model_name}: FAILED - {result['error']} (Time: {training_time:.2f}s)")
            else:
                logger.info(f"{model_name}: SUCCESS (Time: {training_time:.2f}s)")
                
            if 'error' not in result:
                summary = result['summary']
                cv_score = summary.get('cv_score', 'N/A')
                test_metrics = summary.get('test_metrics', {})
                accuracy = test_metrics.get('accuracy', 'N/A') 
                

                logger.info(f"  {model_name}:")
                logger.info(f"    CV Score: {cv_score}")
                logger.info(f"    Test Accuracy: {accuracy}")
                logger.info(f"    Test F1: {test_metrics.get('f1_score', 'N/A')}")
                logger.info(f"    Features: {summary.get('n_features', 'N/A')}")
                logger.info("")

        logger.info("✓ Predictive model training pipeline completed!")

    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
