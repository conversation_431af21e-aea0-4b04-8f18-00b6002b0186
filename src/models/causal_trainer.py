import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import warnings
import logging
import numpy as np
import pandas as pd

from datetime import datetime
from tabulate import tabulate
from typing import Dict, Optional, Tuple, Any
from econml.dml import CausalForestDML, LinearDML

from sklearn.utils import resample
from sklearn.decomposition import PCA
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.feature_selection import VarianceThreshold, SelectKBest, f_classif

from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LogisticRegression, LinearRegression, Ridge, Lasso

import src.models.interface.icausal_model as interface_module
ICausalModel = interface_module.ICausalModel

from src.common.utils import save_model, get_model_save_path

class CausalModelTrainer(ICausalModel):
    """Implementation of causal inference model trainer."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.causal_model = None
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_names = None
        self.model_type = None

        self.ate = None
        self.ate_std = None
        self.treatment_effects_test = None
        self.model_reliability = None
        self.first_stage_scores = None

        self._validate_inputs()

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    # Pre-processing data
    def preprocess_data(self, for_causal_forest: bool = False) -> Tuple[pd.DataFrame, pd.Series, pd.Series]:
        """Preprocess data for causal inference."""
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or
               col in ['patient_id', 'appointment_id',
                       self.treatment_column, self.outcome_column]]

        x = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
        t = self.data[self.treatment_column]
        y = self.data[self.outcome_column]

        x_encoded = pd.get_dummies(x, drop_first=True)

        if for_causal_forest:
            self.logger.info("Applying enhanced preprocessing for Causal Forest...")

            # Remove constant features
            constant_features = [col for col in x_encoded.columns if x_encoded[col].nunique() <= 1]
            if constant_features:
                self.logger.warning(f"Removing {len(constant_features)} constant features")
                x_encoded = x_encoded.drop(columns=constant_features)

            # Aggressive correlation removal
            correlation_threshold = 0.90
            correlation_matrix = x_encoded.corr().abs()
            upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))

            high_corr_features = [column for column in upper_triangle.columns if
                                  any(upper_triangle[column] > correlation_threshold)]

            if high_corr_features:
                self.logger.warning(f"Removing {len(high_corr_features)} highly correlated features")
                x_encoded = x_encoded.drop(columns=high_corr_features)

            # Feature selection based on variance
            variance_selector = VarianceThreshold(threshold=0.01)
            x_variance_selected = variance_selector.fit_transform(x_encoded)
            selected_features = x_encoded.columns[variance_selector.get_support()]

            removed_low_var = len(x_encoded.columns) - len(selected_features)
            if removed_low_var > 0:
                self.logger.warning(f"Removed {removed_low_var} low variance features")
                x_encoded = pd.DataFrame(x_variance_selected, columns=selected_features, index=x_encoded.index)

            # Limit feature count for stability
            max_features = min(10, len(x_encoded.columns))
            if len(x_encoded.columns) > max_features:
                selector = SelectKBest(score_func=f_classif, k=max_features)
                x_selected = selector.fit_transform(x_encoded, t)
                selected_feature_names = x_encoded.columns[selector.get_support()]

                self.logger.warning(f"Selected top {max_features} features from {len(x_encoded.columns)}")
                x_encoded = pd.DataFrame(x_selected, columns=selected_feature_names, index=x_encoded.index)

            # Apply PCA if still too many features
            if len(x_encoded.columns) > 8:
                n_components = min(8, len(x_encoded.columns))
                pca = PCA(n_components=n_components, random_state=42)
                X_pca = pca.fit_transform(x_encoded)

                pca_columns = [f'PC{i + 1}' for i in range(n_components)]
                x_encoded = pd.DataFrame(X_pca, columns=pca_columns, index=x_encoded.index)

                self.logger.warning(f"Applied PCA: reduced to {n_components} principal components")

        else:
            # Standard correlation removal
            correlation_matrix = x_encoded.corr().abs()
            upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))

            high_corr_features = [column for column in upper_triangle.columns if any(
                upper_triangle[column] > 0.95)]
            if high_corr_features:
                self.logger.warning(f"Removing {len(high_corr_features)} highly correlated features")
                x_encoded = x_encoded.drop(columns=high_corr_features)

        self.feature_names = x_encoded.columns.tolist()
        self.logger.info(f"Final feature count: {len(self.feature_names)}")

        return x_encoded, t, y

    # Split data
    def split_data(self, for_causal_forest: bool = False) -> Tuple[
        pd.DataFrame, pd.DataFrame, pd.Series, pd.Series, pd.Series, pd.Series]:
        """Split data into training and testing sets."""
        x, t, y = self.preprocess_data(for_causal_forest=for_causal_forest)

        if for_causal_forest and len(x) > 20000:
            self.logger.info(f"Subsampling data for Causal Forest: {len(x)} -> 20000 samples")

            x_sub, t_sub, y_sub = resample(
                x, t, y, n_samples=20000, random_state=self.random_state, stratify=t)
            x, t, y = x_sub, t_sub, y_sub

        X_train, X_test, T_train, T_test, Y_train, Y_test = train_test_split(
            x, t, y, test_size=self.test_size, random_state=self.random_state, stratify=t)

        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )

        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test),
            columns=X_test.columns,
            index=X_test.index
        )

        self.logger.info(f"Training set: {X_train_imputed.shape}, Test set: {X_test_imputed.shape}")

        return X_train_imputed, X_test_imputed, T_train, T_test, Y_train, Y_test

    # Train model for causal inference
    def train(self,
              model_type: str = 'causal_forest',
              outcome_model: str = 'random_forest',
              treatment_model: str = 'random_forest',
              custom_params: Optional[Dict] = None) -> Any:
        """Train the causal model."""
        self.model_type = model_type

        use_enhanced_preprocessing = (model_type == 'causal_forest')
        X_train, X_test, T_train, T_test, Y_train, Y_test = self.split_data(
            for_causal_forest=use_enhanced_preprocessing)

        # For causal inference, we always use regression models for first-stage
        # even when outcome/treatment are binary, because we need continuous predictions
        outcome_task = 'regression'
        treatment_task = 'regression'

        self.logger.info(f"Using regression models for causal inference")
        self.logger.info(f"Outcome unique values: {Y_train.nunique()}, Treatment unique values: {T_train.nunique()}")

        outcome_est = self._get_base_model(outcome_model, task=outcome_task)
        treatment_est = self._get_base_model(treatment_model, task=treatment_task)

        # Initialize the causal model
        if model_type == 'causal_forest':
            forest_params = custom_params or {}

            causal_forest_configs = [
                {},
                {'n_estimators': 50, 'max_depth': 5},
                {'n_estimators': 20, 'max_depth': 3, 'min_samples_split': 20, 'min_samples_leaf': 10}
            ]

            self.causal_model = None
            last_error = None

            for i, config in enumerate(causal_forest_configs):
                try:
                    final_params = {**config, **forest_params}
                    self.logger.info(f"Attempting Causal Forest configuration {i + 1}: {final_params}")

                    self.causal_model = CausalForestDML(
                        model_y=outcome_est,
                        model_t=treatment_est,
                        random_state=self.random_state,
                        **final_params
                    )
                    self.logger.info(f"Causal Forest configuration {i + 1} created successfully")
                    break

                except Exception as e:
                    last_error = e
                    self.logger.warning(f"Causal Forest configuration {i + 1} failed: {str(e)}")
                    continue

            if self.causal_model is None:
                self.logger.error("All Causal Forest configurations failed")
                if last_error:
                    raise last_error
                else:
                    raise ValueError("Failed to create Causal Forest model")

        elif model_type == 'linear_dml':
            try:
                self.logger.info("Creating Linear DML model...")
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    self.causal_model = LinearDML(
                        model_y=outcome_est,
                        model_t=treatment_est,
                        random_state=self.random_state,
                        **(custom_params or {})
                    )
                    self.logger.info("Linear DML model created successfully")

                    if w:
                        for warning in w:
                            self.logger.warning(f"LinearDML initialization warning: {warning.message}")
            except Exception as e:
                self.logger.error(f"Failed to create Linear DML model: {e}")
                raise
        else:
            raise ValueError(f"Model type '{model_type}' is not supported.")

        # Verify model was created
        if self.causal_model is None:
            raise ValueError(f"Failed to initialize {model_type} model")

        self.logger.info(f"Model {model_type} initialized successfully")

        # Fit the model
        try:
            self.logger.info("Starting model fitting...")
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                self.causal_model.fit(Y_train, T_train, X=X_train)

                if w:
                    for warning in w:
                        self.logger.warning(f"Model fitting warning: {warning.message}")

            self.logger.info("Model fitting completed successfully")

        except Exception as e:
            self.logger.error(f"Error during model fitting: {e}")
            raise

        # Evaluate the model
        self.logger.info("Starting model evaluation...")
        self._evaluate_model(X_test, T_test, Y_test)
        self.logger.info("Model evaluation completed")

        # Save the model
        try:
            model_path = get_model_save_path(self.model_name, self.model_type, use_user_dir=False)
            self.logger.info(f"Saving model to {model_path}")

            metadata = {
                'model_name': self.model_name,
                'model_type': self.model_type,
                'treatment_column': self.treatment_column,
                'outcome_column': self.outcome_column,
                'ate': self.ate,
                'ate_std': self.ate_std,
                'model_reliability': self.model_reliability,
                'first_stage_scores': self.first_stage_scores,
                'feature_names': self.feature_names,
                'training_timestamp': datetime.now().isoformat(),
                'data_shape': self.data.shape
            }

            save_model(self.causal_model, model_path, metadata)
            self.logger.info(f"Model saved successfully to {model_path}")

        except Exception as e:
            self.logger.error(f"Error saving model: {e}")

        return self.causal_model

    # Evaluate the causal model
    def evaluate(self, X_test: pd.DataFrame = None, T_test: pd.Series = None, Y_test: pd.Series = None) -> Dict[
        str, Any]:
        """Evaluate the causal model."""
        if X_test is not None and T_test is not None and Y_test is not None:
            self._evaluate_model(X_test, T_test, Y_test)

        return {
            'ate': self.ate,
            'ate_std': self.ate_std,
            'reliability': self.model_reliability,
            'first_stage_scores': self.first_stage_scores
        }

    def get_model_summary(self) -> Dict[str, Any]:
        """Get model summary."""
        return {
            'model_type': self.model_type,
            'ate': self.ate,
            'ate_std': self.ate_std,
            'reliability': self.model_reliability,
            'first_stage_scores': self.first_stage_scores
        }

    def _validate_inputs(self):
        """Validate input parameters and data."""
        if self.treatment_column not in self.data.columns:
            raise ValueError(f"Treatment column '{self.treatment_column}' not found in data")

        if self.outcome_column not in self.data.columns:
            raise ValueError(f"Outcome column '{self.outcome_column}' not found in data")

        if self.data.empty:
            raise ValueError("Data cannot be empty")

        if self.test_size <= 0 or self.test_size >= 1:
            raise ValueError("test_size must be between 0 and 1")

    def _get_base_model(self, model_name: str, task: str):
        """Get a base model for an outcome or treatment prediction."""
        # For causal inference, we always use regression models
        model_mapping = {
            'regression': {
                'random_forest': RandomForestRegressor,
                'linear': LinearRegression,
                'ridge': Ridge,
                'lasso': Lasso
            }
        }

        try:
            model_class = model_mapping[task][model_name]
            if model_class in [LinearRegression, Ridge, Lasso]:
                if model_class == Ridge:
                    return model_class(alpha=1.0)
                elif model_class == Lasso:
                    return model_class(alpha=0.1)
                else:
                    return model_class()
            else:
                return model_class(random_state=self.random_state)
        except KeyError as e:
            raise ValueError(f"Model '{model_name}' for task '{task}' is not supported.") from e

    def _evaluate_model(self, X_test: pd.DataFrame, T_test: pd.Series, Y_test: pd.Series):
        """Evaluate the causal model and check for reliability issues."""
        try:
            self.logger.info("Starting model evaluation...")
            self.logger.info(f"Test set shape: X={X_test.shape}, T={T_test.shape}, Y={Y_test.shape}")

            treatment_effects = self.causal_model.effect(X_test)
            self.logger.info(f"Treatment effects computed: shape={treatment_effects.shape}")

            self.ate = np.mean(treatment_effects)
            self.ate_std = np.std(treatment_effects)

            self.logger.info(f"ATE calculated: {self.ate}")
            self.logger.info(f"ATE Std calculated: {self.ate_std}")

            self.model_reliability = self._check_model_reliability(treatment_effects, T_test, Y_test)
            self.first_stage_scores = self._evaluate_first_stage_models(X_test, T_test, Y_test)
            self._log_evaluation_results()

        except Exception as e:
            self.logger.error(f"Error in model evaluation: {e}", exc_info=True)
            self.ate = None
            self.ate_std = None
            self.model_reliability = {'is_reliable': False, 'warnings': [f"Evaluation failed: {e}"]}
            self.first_stage_scores = None

    def _check_model_reliability(self, treatment_effects: np.ndarray, T_test: pd.Series, Y_test: pd.Series) -> Dict[
        str, Any]:
        """Check for various reliability issues in the causal model."""
        reliability = {
            'is_reliable': True,
            'warnings': [],
            'metrics': {}
        }

        ate_magnitude = abs(self.ate)
        std_magnitude = abs(self.ate_std)

        if ate_magnitude > 1e6:
            reliability['is_reliable'] = False
            reliability['warnings'].append(f"Extremely large ATE magnitude: {ate_magnitude:.2e}")

        if std_magnitude > 1e6:
            reliability['is_reliable'] = False
            reliability['warnings'].append(f"Extremely large ATE standard deviation: {std_magnitude:.2e}")

        if np.any(np.isnan(treatment_effects)) or np.any(np.isinf(treatment_effects)):
            reliability['is_reliable'] = False
            reliability['warnings'].append("Treatment effects contain NaN or infinite values")

        if self.ate != 0:
            cv = abs(self.ate_std / self.ate)
            reliability['metrics']['coefficient_of_variation'] = cv
            if cv > 10:
                reliability['warnings'].append(f"High coefficient of variation: {cv:.2f}")

        effect_range = np.max(treatment_effects) - np.min(treatment_effects)
        reliability['metrics']['effect_range'] = effect_range
        reliability['metrics']['effect_iqr'] = np.percentile(treatment_effects, 75) - np.percentile(treatment_effects,25)

        if effect_range > 1e6:
            reliability['warnings'].append(f"Extremely wide effect range: {effect_range:.2e}")

        return reliability

    def _evaluate_first_stage_models(self, X_test: pd.DataFrame, T_test: pd.Series, Y_test: pd.Series) -> Dict[
        str, Any]:
        """Evaluate the performance of first-stage models."""
        scores = {}

        try:
            # Try to get first stage models if available
            if hasattr(self.causal_model, 'models_y') and hasattr(self.causal_model, 'models_t'):
                # For outcome model
                if hasattr(self.causal_model.models_y[0], 'predict'):
                    y_pred = self.causal_model.models_y[0].predict(X_test)
                    scores['outcome_mse'] = mean_squared_error(Y_test, y_pred)
                    scores['outcome_r2'] = r2_score(Y_test, y_pred)

                # For treatment model
                if hasattr(self.causal_model.models_t[0], 'predict'):
                    t_pred = self.causal_model.models_t[0].predict(X_test)
                    scores['treatment_mse'] = mean_squared_error(T_test, t_pred)
                    scores['treatment_r2'] = r2_score(T_test, t_pred)

        except Exception as e:
            self.logger.warning(f"Could not evaluate first-stage models: {e}")
            scores['evaluation_error'] = str(e)

        return scores

    def _log_evaluation_results(self):
        """Log evaluation results with appropriate warnings."""
        self.logger.info(f"ATE: {self.ate}, ATE Std: {self.ate_std}")

        if self.model_reliability and not self.model_reliability['is_reliable']:
            self.logger.warning("MODEL RELIABILITY ISSUES DETECTED:")
            for warning in self.model_reliability['warnings']:
                self.logger.warning(f"  - {warning}")

        if self.first_stage_scores:
            self.logger.info("First-stage model performance:")
            for metric, value in self.first_stage_scores.items():
                if isinstance(value, float):
                    self.logger.info(f"  {metric}: {value:.4f}")
                else:
                    self.logger.info(f"  {metric}: {value}")