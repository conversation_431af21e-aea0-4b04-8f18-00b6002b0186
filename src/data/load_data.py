import os
import sys
import logging
import pandas as pd
from pathlib import Path
from typing import Optional, Union, Dict

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from globals.basic_settings import settings, app_settings, DATA_SOURCE, DATA_SOURCE_BASE_PATH, PRIMARY_DATA_FILE, PREPROCESSED_DATA_FILE, FEATURE_DATA_FILE, FINAL_DATA_FILE

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataLoader:
    """Centralized data loading functionality"""

    @staticmethod
    def _load_csv(file_path: Union[str, Path], description: str = "data") -> pd.DataFrame:
        """
        Load CSV file with error handling.

        Args:
            file_path: Path to the CSV file
            description: Description of the data being loaded

        Returns:
            pd.DataFrame: Loaded data

        Raises:
            FileNotFoundError: If file doesn't exist
            pd.errors.EmptyDataError: If file is empty
            Exception: For other loading errors
        """
        file_path = Path(file_path)

        try:
            if not file_path.exists():
                raise FileNotFoundError(
                    f"{description} file not found: {file_path}")

            logger.info(f"Loading {description} from {file_path}")
            data = pd.read_csv(file_path)

            if data.empty:
                raise pd.errors.EmptyDataError(
                    f"{description} file is empty: {file_path}")

            logger.info(
                f"{description} loaded successfully: {data.shape[0]} rows, {data.shape[1]} columns")
            return data

        except FileNotFoundError as e:
            logger.error(str(e))
            raise
        except pd.errors.EmptyDataError as e:
            logger.error(str(e))
            raise
        except Exception as e:
            logger.error(f"Error loading {description}: {str(e)}")
            raise


def get_data_file_path(filename: str, stage: str = 'raw') -> Path:
    """
    Get the full path to a data file based on the stage.

    Args:
        filename: The name of the data file
        stage: The stage of the data ('raw', 'interim', 'processed')

    Returns:
        Path to the file
    """
    base_dir = Path(DATA_SOURCE_BASE_PATH)

    stage_map = {
        'raw': base_dir / 'raw',
        'interim': base_dir / 'interim',
        'processed': base_dir / 'processed',
    }

    if stage not in stage_map:
        raise ValueError(
            f"Unknown stage: {stage}. Valid stages: {list(stage_map.keys())}")

    return stage_map[stage] / filename

# Load source data


def load_source_data() -> pd.DataFrame:
    """
    Load source data from configured path.

    Returns:
        pd.DataFrame: Source data
    """
    file_path = get_data_file_path(PRIMARY_DATA_FILE, 'raw')
    return DataLoader._load_csv(file_path, "source data")

# Load cleaned data


def load_preprocessed_data() -> pd.DataFrame:
    """
    Load preprocessed data from configured path.

    Returns:
        pd.DataFrame: Preprocessed data
    """
    file_path = get_data_file_path(PREPROCESSED_DATA_FILE, 'interim')
    return DataLoader._load_csv(file_path, "preprocessed data")

# Load feature engineered data


def load_app_feature_data() -> pd.DataFrame:
    """
    Load feature-engineered data from the app data directory.

    Returns:
        pd.DataFrame: Feature-engineered medical appointment data
    """
    file_path = get_data_file_path(FEATURE_DATA_FILE, 'processed')
    return DataLoader._load_csv(file_path, "feature data")

# Load final dataset


def load_final_data() -> pd.DataFrame:
    """
    Load final data from the app data directory.

    Returns:
        pd.DataFrame: Final medical appointment data
    """
    file_path = get_data_file_path(FINAL_DATA_FILE, 'processed')
    return DataLoader._load_csv(file_path, "final data")

# Additional data loading functions


def load_data_by_stage(stage: str) -> pd.DataFrame:
    """
    Load data by stage name.

    Args:
        stage: One of 'raw', 'source', 'preprocessed', 'feature', 'final'

    Returns:
        pd.DataFrame: Requested data

    Raises:
        ValueError: If stage is not recognized
    """
    stage = stage.lower()

    stage_map = {
        'raw': load_source_data,
        'source': load_source_data,
        'preprocessed': load_preprocessed_data,
        'feature': load_app_feature_data,
        'final': load_final_data
    }

    if stage not in stage_map:
        available_stages = ', '.join(stage_map.keys())
        raise ValueError(
            f"Unknown stage: {stage}. Available stages: {available_stages}")

    return stage_map[stage]()


def check_data_availability() -> Dict[str, Dict[str, Union[str, bool, float]]]:
    """
    Check which data files are available.

    Returns:
        dict: Dictionary with file availability status
    """
    status = {}

    # Define files to check
    files_to_check = [
        ('source', PRIMARY_DATA_FILE, 'raw'),
        ('preprocessed', PREPROCESSED_DATA_FILE, 'interim'),
        ('feature', FEATURE_DATA_FILE, 'processed'),
        ('final', FINAL_DATA_FILE, 'processed'),
    ]

    for key, filename, stage in files_to_check:
        full_path = get_data_file_path(filename, stage)
        status[key] = {
            'path': str(full_path),
            'exists': full_path.exists(),
            'size': full_path.stat().st_size / (1024**2) if full_path.exists() else 0,  # Size in MB
            'stage': stage
        }

    return status


def get_data_info(data: pd.DataFrame) -> Dict[str, any]:
    """
    Get comprehensive information about a dataframe.

    Args:
        data: DataFrame to analyze

    Returns:
        dict: Dictionary with data information
    """
    info = {
        'shape': data.shape,
        'columns': list(data.columns),
        'dtypes': {col: str(dtype) for col, dtype in data.dtypes.items()},
        'missing_values': data.isnull().sum().to_dict(),
        'missing_percentage': (data.isnull().sum() / len(data) * 100).round(2).to_dict(),
        'memory_usage_mb': data.memory_usage(deep=True).sum() / (1024**2),
        'numeric_columns': list(data.select_dtypes(include=['int64', 'float64']).columns),
        'categorical_columns': list(data.select_dtypes(include=['object', 'category']).columns),
        'datetime_columns': list(data.select_dtypes(include=['datetime64']).columns),
        'bool_columns': list(data.select_dtypes(include=['bool']).columns)
    }

    # Add basic statistics for numeric columns
    numeric_data = data.select_dtypes(include=['int64', 'float64'])
    if not numeric_data.empty:
        info['numeric_stats'] = numeric_data.describe().to_dict()

    return info


def save_data(data: pd.DataFrame, filename: str, stage: str = 'processed') -> Path:
    """
    Save DataFrame to CSV file.

    Args:
        data: DataFrame to save
        filename: Name of the file to save
        stage: Data stage ('raw', 'interim', 'processed')

    Returns:
        Path: Path to the saved file
    """
    try:
        file_path = get_data_file_path(filename, stage)
        file_path.parent.mkdir(parents=True, exist_ok=True)

        data.to_csv(file_path, index=False)
        logger.info(f"Data saved successfully to {file_path}")
        logger.info(f"Saved {data.shape[0]} rows and {data.shape[1]} columns")

        return file_path

    except Exception as e:
        logger.error(f"Error saving data: {e}")
        raise

# Convenience function for backward compatibility


def load_data() -> pd.DataFrame:
    """
    Load the default dataset (source data).

    Returns:
        pd.DataFrame: Default dataset
    """
    return load_source_data()


# Main execution
if __name__ == "__main__":
    print("Medical Appointment Data Loader")
    print("=" * 50)

    # Check data availability
    print("\nChecking data availability...")
    availability = check_data_availability()

    for key, status in availability.items():
        exists = "[+]" if status['exists'] else "[-]"
        size = f"{status['size']:.2f} MB" if status['exists'] else "N/A"
        print(f"{exists} {key:15} [{status['stage']:10}]: {size:>10}")

    print("\nTesting data loading...")

    # Try loading available data
    for stage in ['source', 'preprocessed', 'feature', 'final']:
        if availability.get(stage, {}).get('exists', False):
            try:
                data = load_data_by_stage(stage)
                info = get_data_info(data)
                print(f"\n{stage.upper()} DATA:")
                print(f"  Shape: {info['shape']}")
                print(f"  Memory: {info['memory_usage_mb']:.2f} MB")
                print(f"  Numeric columns: {len(info['numeric_columns'])}")
                print(
                    f"  Categorical columns: {len(info['categorical_columns'])}")
                print(
                    f"  Missing values: {sum(info['missing_values'].values())} total")
                break
            except Exception as e:
                print(f"Error loading {stage} data: {e}")
        else:
            print(f"Skipping {stage} data (not found)")