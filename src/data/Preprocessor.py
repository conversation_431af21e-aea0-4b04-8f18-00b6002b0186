import os
import sys
import logging
import pandas as pd

from typing import Optional, Union
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data import load_data as ld
from globals.basic_settings import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Preprocessor:
    """
    A class to handle preprocessing of medical appointment data.
    """

    def __init__(self, data: Optional[pd.DataFrame] = None):
        """
        Initialize the preprocessor.

        Args:
            data: Optional DataFrame to preprocess. If None, loads source data.
        """
        if data is None:
            logger.info("Loading source data...")
            self.data = ld.load_source_data()
        else:
            self.data = data.copy(deep=True)

        self.original_shape = self.data.shape
        logger.info(f"Initialized preprocessor with data shape: {self.original_shape}")

    def rename_columns(self) -> 'Preprocessor':
        """
        Standardize column names: lowercase, remove spaces/hyphens, rename specific columns.

        Returns:
            self: For method chaining
        """
        try:
            # Standardize all column names
            self.data.columns = [
                col.strip().lower().replace("-", "").replace(" ", "_")
                for col in self.data.columns
            ]

            # Rename specific columns for clarity
            column_mapping = {
                'patientid': 'patient_id',
                'appointmentid': 'appointment_id',
                'scheduledday': 'scheduled_day',
                'appointmentday': 'appointment_day',
                'noshow': 'no_show',
                'hipertension': 'hypertension',  # Fix typo
                'handcap': 'handicap'
            }

            self.data.rename(columns=column_mapping, inplace=True)
            logger.info("Column names standardized successfully")

        except Exception as e:
            logger.error(f"Error renaming columns: {e}")
            raise

        return self

    def drop_missing_values(self) -> 'Preprocessor':
        """
        Drop rows with missing values.

        Returns:
            self: For method chaining
        """
        try:
            initial_rows = len(self.data)
            self.data.dropna(inplace=True)
            dropped_rows = initial_rows - len(self.data)

            if dropped_rows > 0:
                logger.info(f"Dropped {dropped_rows} rows with missing values")
            else:
                logger.info("No missing values found")

        except Exception as e:
            logger.error(f"Error dropping missing values: {e}")
            raise

        return self

    def process_datatypes(self) -> 'Preprocessor':
        """
        Convert data types and create new features.

        Returns:
            self: For method chaining
        """
        logger.info(f"Processing data with {self.data.shape[0]} rows and {self.data.shape[1]} columns")

        try:
            self._extracted_from_process_datatypes()
        except Exception as e:
            logger.error(f"Error in process_datatypes: {e}")
            raise

        return self

    def _extracted_from_process_datatypes(self):
        # Convert ID columns to int64
        id_columns = ['patient_id', 'appointment_id']
        for col in id_columns:
            if col in self.data.columns:
                self.data[col] = self.data[col].astype('int64')

        # Convert binary columns to int64
        binary_columns = ['scholarship', 'hypertension', 'diabetes', 'alcoholism', 'sms_received']
        for col in binary_columns:
            if col in self.data.columns:
                self.data[col] = self.data[col].astype('int64')

        # Convert date columns to datetime
        date_columns = ['scheduled_day', 'appointment_day']
        for col in date_columns:
            if col in self.data.columns:
                self.data[col] = pd.to_datetime(self.data[col])

        # Extract day of week from appointment_day
        if 'appointment_day' in self.data.columns:
            self.data['appointment_day_of_week'] = self.data['appointment_day'].dt.day_name()

        # Convert categorical columns
        categorical_columns = ['gender', 'neighbourhood', 'handicap', 'appointment_day_of_week']
        for col in categorical_columns:
            if col in self.data.columns:
                self.data[col] = self.data[col].astype('category')

        # Process no_show column
        if 'no_show' in self.data.columns:
            self.data['no_show'] = self.data['no_show'].replace({'No': 0, 'Yes': 1})
            self.data['no_show'] = self.data['no_show'].astype('int64')

        # Fix age column (replace -1 with 0)
        if 'age' in self.data.columns:
            self.data['age'] = self.data['age'].replace(-1, 0)
            self.data['age'] = self.data['age'].astype('int64')

        # Convert sms_received to boolean
        if 'sms_received' in self.data.columns:
            self.data['sms_received'] = self.data['sms_received'].astype('bool')

        logger.info("Data type processing completed successfully")

    def validate_data(self) -> 'Preprocessor':
        """
        Validate the processed data.

        Returns:
            self: For method chaining
        """
        try:
            # Check for duplicate appointment IDs
            if 'appointment_id' in self.data.columns:
                duplicates = self.data['appointment_id'].duplicated().sum()
                if duplicates > 0:
                    logger.warning(f"Found {duplicates} duplicate appointment IDs")

            # Check age range
            if 'age' in self.data.columns:
                invalid_ages = ((self.data['age'] < 0) | (self.data['age'] > 120)).sum()
                if invalid_ages > 0:
                    logger.warning(f"Found {invalid_ages} records with invalid ages")

            # Check date consistency
            if all(col in self.data.columns for col in ['scheduled_day', 'appointment_day']):
                future_appointments = (self.data['scheduled_day'] > self.data['appointment_day']).sum()
                if future_appointments > 0:
                    logger.warning(f"Found {future_appointments} appointments scheduled after appointment day")

            logger.info("Data validation completed")

        except Exception as e:
            logger.error(f"Error validating data: {e}")
            # Don't raise here, just log the warning

        return self

    def save_cleaned_data(self, filename: Optional[str] = None, stage: str = 'interim') -> Path:
        """
        Save the cleaned DataFrame to a CSV file.

        Args:
            filename: Optional custom filename. If None, uses default from config
            stage: Data stage directory ('interim' for cleaned data)

        Returns:
            Path: Path to the saved file
        """
        try:
            if filename is None:
                filename = config.get('PREPROCESSED_DATA_FILE', 'medical_appointment_no_show_cleaned.csv')

            file_path = ld.save_data(self.data, filename, stage)

            logger.info("Preprocessed data saved successfully")
            logger.info(f"Original shape: {self.original_shape}, Final shape: {self.data.shape}")

            return file_path

        except Exception as e:
            logger.error(f"Error saving cleaned data: {e}")
            raise

    def get_preprocessing_summary(self) -> dict:
        """
        Get a summary of preprocessing steps and data changes.

        Returns:
            dict: Summary of preprocessing
        """
        return {
            'original_shape': self.original_shape,
            'final_shape': self.data.shape,
            'rows_removed': self.original_shape[0] - self.data.shape[0],
            'columns_added': self.data.shape[1] - self.original_shape[1],
            'data_types': self.data.dtypes.value_counts().to_dict(),
            'memory_usage_mb': self.data.memory_usage(deep=True).sum() / (1024 ** 2)
        }

    def get_data(self) -> pd.DataFrame:
        """
        Get the processed data.

        Returns:
            pd.DataFrame: The processed data
        """
        return self.data.copy()


def main():
    """Main function to run the preprocessing pipeline."""
    try:
        # Initialize preprocessor
        preprocessor = Preprocessor()

        # Run preprocessing pipeline with method chaining
        preprocessor = (preprocessor
                        .rename_columns()
                        .drop_missing_values()
                        .process_datatypes()
                        .validate_data())

        # Get preprocessing summary
        summary = preprocessor.get_preprocessing_summary()
        logger.info("Preprocessing Summary:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")

        # Save the cleaned data
        saved_path = preprocessor.save_cleaned_data()
        logger.info(f"Preprocessing completed. Data saved to: {saved_path}")

        return preprocessor.get_data()

    except Exception as e:
        logger.error(f"Preprocessing failed: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    try:
        processed_data = main()
        print(f"\nPreprocessing completed successfully!")
        print(f"Final data shape: {processed_data.shape}")
        print(f"\nData types:")
        print(processed_data.dtypes.value_counts())
    except Exception as e:
        print(f"An error occurred: {e}")